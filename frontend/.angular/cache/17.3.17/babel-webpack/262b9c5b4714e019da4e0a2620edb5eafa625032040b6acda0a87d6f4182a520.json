{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/product.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nfunction HomeComponent_span_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const category_r1 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", category_r1, \" \");\n  }\n}\nfunction HomeComponent_span_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Fruits \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_span_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Vegetables \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_span_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Dairy \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_span_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Bakery \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_span_66_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Meat \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_span_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 45);\n    i0.ɵɵelement(1, \"i\", 46);\n    i0.ɵɵtext(2, \"Grains \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction HomeComponent_div_76_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47);\n    i0.ɵɵelement(1, \"div\", 48);\n    i0.ɵɵelementStart(2, \"p\", 49);\n    i0.ɵɵtext(3, \"Loading products...\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction HomeComponent_div_77_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"div\", 53)(2, \"div\", 54);\n    i0.ɵɵelement(3, \"i\", 55);\n    i0.ɵɵelementStart(4, \"h6\", 23);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 56);\n    i0.ɵɵtext(7);\n    i0.ɵɵpipe(8, \"number\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 57);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 58);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const product_r2 = ctx.$implicit;\n    const i_r3 = ctx.index;\n    i0.ɵɵclassProp(\"d-none\", i_r3 >= 8);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(product_r2.productName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u20B9\", i0.ɵɵpipeBind2(8, 10, product_r2.price, \"1.2-2\"), \"\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(product_r2.category || \"General\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"bg-success\", product_r2.quantity > 0)(\"bg-danger\", product_r2.quantity <= 0);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", product_r2.quantity > 0 ? \"In Stock\" : \"Out of Stock\", \" \");\n  }\n}\nfunction HomeComponent_div_77_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 52)(2, \"div\", 53)(3, \"div\", 54);\n    i0.ɵɵelement(4, \"i\", 55);\n    i0.ɵɵelementStart(5, \"h6\", 23);\n    i0.ɵɵtext(6, \"Fresh Apples\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 56);\n    i0.ɵɵtext(8, \"\\u20B9150.00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 57);\n    i0.ɵɵtext(10, \"Fruits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 59);\n    i0.ɵɵtext(12, \"In Stock\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 52)(14, \"div\", 53)(15, \"div\", 54);\n    i0.ɵɵelement(16, \"i\", 60);\n    i0.ɵɵelementStart(17, \"h6\", 23);\n    i0.ɵɵtext(18, \"Fresh Bananas\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"p\", 56);\n    i0.ɵɵtext(20, \"\\u20B980.00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"p\", 57);\n    i0.ɵɵtext(22, \"Fruits\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"span\", 59);\n    i0.ɵɵtext(24, \"In Stock\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(25, \"div\", 52)(26, \"div\", 53)(27, \"div\", 54);\n    i0.ɵɵelement(28, \"i\", 61);\n    i0.ɵɵelementStart(29, \"h6\", 23);\n    i0.ɵɵtext(30, \"Fresh Milk\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"p\", 56);\n    i0.ɵɵtext(32, \"\\u20B960.00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"p\", 57);\n    i0.ɵɵtext(34, \"Dairy\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"span\", 59);\n    i0.ɵɵtext(36, \"In Stock\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(37, \"div\", 52)(38, \"div\", 53)(39, \"div\", 54);\n    i0.ɵɵelement(40, \"i\", 62);\n    i0.ɵɵelementStart(41, \"h6\", 23);\n    i0.ɵɵtext(42, \"Whole Wheat Bread\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(43, \"p\", 56);\n    i0.ɵɵtext(44, \"\\u20B940.00\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(45, \"p\", 57);\n    i0.ɵɵtext(46, \"Bakery\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(47, \"span\", 59);\n    i0.ɵɵtext(48, \"In Stock\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction HomeComponent_div_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵtemplate(1, HomeComponent_div_77_div_1_Template, 13, 13, \"div\", 50)(2, HomeComponent_div_77_ng_container_2_Template, 49, 0, \"ng-container\", 51);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r3.featuredProducts);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r3.featuredProducts.length === 0);\n  }\n}\n/**\n * Home page component displaying featured products and categories.\n *\n * <AUTHOR> Singhal\n * @version 1.0.0\n */\nexport class HomeComponent {\n  constructor(productService) {\n    this.productService = productService;\n    this.featuredProducts = [];\n    this.categories = [];\n    this.loading = true;\n  }\n  ngOnInit() {\n    this.loadFeaturedProducts();\n    this.loadCategories();\n  }\n  loadFeaturedProducts() {\n    this.productService.getInStockProducts().subscribe({\n      next: products => {\n        this.featuredProducts = products.slice(0, 8); // Show only first 8 products\n        this.loading = false;\n      },\n      error: error => {\n        console.error('Error loading featured products:', error);\n        this.loading = false;\n      }\n    });\n  }\n  loadCategories() {\n    this.productService.getAllCategories().subscribe({\n      next: categories => {\n        this.categories = categories;\n      },\n      error: error => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function HomeComponent_Factory(t) {\n      return new (t || HomeComponent)(i0.ɵɵdirectiveInject(i1.ProductService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: HomeComponent,\n      selectors: [[\"app-home\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 95,\n      vars: 9,\n      consts: [[1, \"hero-section\"], [1, \"container\"], [1, \"row\", \"align-items-center\"], [1, \"col-lg-6\"], [1, \"display-4\", \"fw-bold\", \"mb-4\", \"text-white\"], [1, \"lead\", \"mb-4\", \"text-light\"], [1, \"d-flex\", \"gap-3\", \"flex-wrap\"], [\"routerLink\", \"/register\", 1, \"btn\", \"btn-light\", \"btn-lg\"], [1, \"fas\", \"fa-user-plus\", \"me-2\"], [\"routerLink\", \"/products/search\", 1, \"btn\", \"btn-outline-light\", \"btn-lg\"], [1, \"fas\", \"fa-search\", \"me-2\"], [1, \"col-lg-6\", \"text-center\"], [1, \"fas\", \"fa-shopping-basket\", \"hero-icon\"], [1, \"py-5\"], [1, \"row\", \"text-center\", \"mb-5\"], [1, \"col\"], [1, \"fw-bold\"], [1, \"text-muted\"], [1, \"row\"], [1, \"col-md-4\", \"mb-4\"], [1, \"card\", \"card-custom\", \"h-100\", \"text-center\", \"p-4\"], [1, \"card-body\"], [1, \"fas\", \"fa-leaf\", \"text-success\", \"mb-3\", \"feature-icon\"], [1, \"card-title\"], [1, \"card-text\"], [1, \"fas\", \"fa-truck\", \"text-primary\", \"mb-3\", \"feature-icon\"], [1, \"fas\", \"fa-shield-alt\", \"text-warning\", \"mb-3\", \"feature-icon\"], [1, \"py-5\", \"bg-light\"], [1, \"row\", \"justify-content-center\"], [1, \"col-md-8\", \"text-center\"], [1, \"category-container\"], [\"class\", \"category-badge\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"category-badge\", 4, \"ngIf\"], [\"class\", \"text-center\", 4, \"ngIf\"], [\"class\", \"row\", 4, \"ngIf\"], [1, \"text-center\", \"mt-4\"], [\"routerLink\", \"/products\", 1, \"btn\", \"btn-primary-custom\", \"btn-lg\"], [1, \"fas\", \"fa-eye\", \"me-2\"], [1, \"py-5\", \"bg-primary-custom\", \"text-white\"], [1, \"container\", \"text-center\"], [1, \"fw-bold\", \"mb-4\"], [1, \"lead\", \"mb-4\"], [1, \"d-flex\", \"justify-content-center\", \"gap-3\", \"flex-wrap\"], [\"routerLink\", \"/admin/login\", 1, \"btn\", \"btn-outline-light\", \"btn-lg\"], [1, \"fas\", \"fa-cogs\", \"me-2\"], [1, \"category-badge\"], [1, \"fas\", \"fa-tag\", \"me-1\"], [1, \"text-center\"], [1, \"spinner-custom\", \"mx-auto\"], [1, \"mt-3\"], [\"class\", \"col-md-3 col-sm-6 mb-4\", 3, \"d-none\", 4, \"ngFor\", \"ngForOf\"], [4, \"ngIf\"], [1, \"col-md-3\", \"col-sm-6\", \"mb-4\"], [1, \"card\", \"product-card\", \"h-100\"], [1, \"card-body\", \"text-center\"], [1, \"fas\", \"fa-apple-alt\", \"text-success\", \"mb-3\", \"product-icon\"], [1, \"product-price\"], [1, \"text-muted\", \"small\"], [1, \"badge\"], [1, \"badge\", \"bg-success\"], [1, \"fas\", \"fa-seedling\", \"text-warning\", \"mb-3\", \"product-icon\"], [1, \"fas\", \"fa-cheese\", \"text-warning\", \"mb-3\", \"product-icon\"], [1, \"fas\", \"fa-bread-slice\", \"text-warning\", \"mb-3\", \"product-icon\"]],\n      template: function HomeComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"section\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h1\", 4);\n          i0.ɵɵtext(5, \" Fresh Groceries Delivered to Your Door \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 5);\n          i0.ɵɵtext(7, \" Shop from our wide selection of fresh fruits, vegetables, dairy products, and more. Get everything you need with just a few clicks! \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"a\", 7);\n          i0.ɵɵelement(10, \"i\", 8);\n          i0.ɵɵtext(11, \"Get Started \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"a\", 9);\n          i0.ɵɵelement(13, \"i\", 10);\n          i0.ɵɵtext(14, \"Browse Products \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(15, \"div\", 11);\n          i0.ɵɵelement(16, \"i\", 12);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(17, \"section\", 13)(18, \"div\", 1)(19, \"div\", 14)(20, \"div\", 15)(21, \"h2\", 16);\n          i0.ɵɵtext(22, \"Why Choose FreshMart?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(23, \"p\", 17);\n          i0.ɵɵtext(24, \"Experience the best online grocery shopping\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(25, \"div\", 18)(26, \"div\", 19)(27, \"div\", 20)(28, \"div\", 21);\n          i0.ɵɵelement(29, \"i\", 22);\n          i0.ɵɵelementStart(30, \"h5\", 23);\n          i0.ɵɵtext(31, \"Fresh & Organic\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"p\", 24);\n          i0.ɵɵtext(33, \" Hand-picked fresh produce and organic products delivered straight from farms to your table. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(34, \"div\", 19)(35, \"div\", 20)(36, \"div\", 21);\n          i0.ɵɵelement(37, \"i\", 25);\n          i0.ɵɵelementStart(38, \"h5\", 23);\n          i0.ɵɵtext(39, \"Fast Delivery\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"p\", 24);\n          i0.ɵɵtext(41, \" Quick and reliable delivery service. Get your groceries delivered within hours of ordering. \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(42, \"div\", 19)(43, \"div\", 20)(44, \"div\", 21);\n          i0.ɵɵelement(45, \"i\", 26);\n          i0.ɵɵelementStart(46, \"h5\", 23);\n          i0.ɵɵtext(47, \"Secure Shopping\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"p\", 24);\n          i0.ɵɵtext(49, \" Safe and secure payment processing with advanced encryption to protect your data. \");\n          i0.ɵɵelementEnd()()()()()()();\n          i0.ɵɵelementStart(50, \"section\", 27)(51, \"div\", 1)(52, \"div\", 14)(53, \"div\", 15)(54, \"h2\", 16);\n          i0.ɵɵtext(55, \"Shop by Category\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(56, \"p\", 17);\n          i0.ɵɵtext(57, \"Explore our wide range of product categories\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(58, \"div\", 28)(59, \"div\", 29)(60, \"div\", 30);\n          i0.ɵɵtemplate(61, HomeComponent_span_61_Template, 3, 1, \"span\", 31)(62, HomeComponent_span_62_Template, 3, 0, \"span\", 32)(63, HomeComponent_span_63_Template, 3, 0, \"span\", 32)(64, HomeComponent_span_64_Template, 3, 0, \"span\", 32)(65, HomeComponent_span_65_Template, 3, 0, \"span\", 32)(66, HomeComponent_span_66_Template, 3, 0, \"span\", 32)(67, HomeComponent_span_67_Template, 3, 0, \"span\", 32);\n          i0.ɵɵelementEnd()()()()();\n          i0.ɵɵelementStart(68, \"section\", 13)(69, \"div\", 1)(70, \"div\", 14)(71, \"div\", 15)(72, \"h2\", 16);\n          i0.ɵɵtext(73, \"Featured Products\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(74, \"p\", 17);\n          i0.ɵɵtext(75, \"Check out our most popular items\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵtemplate(76, HomeComponent_div_76_Template, 4, 0, \"div\", 33)(77, HomeComponent_div_77_Template, 3, 2, \"div\", 34);\n          i0.ɵɵelementStart(78, \"div\", 35)(79, \"a\", 36);\n          i0.ɵɵelement(80, \"i\", 37);\n          i0.ɵɵtext(81, \"View All Products \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(82, \"section\", 38)(83, \"div\", 39)(84, \"h2\", 40);\n          i0.ɵɵtext(85, \"Ready to Start Shopping?\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(86, \"p\", 41);\n          i0.ɵɵtext(87, \" Join thousands of satisfied customers who trust FreshMart for their grocery needs. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(88, \"div\", 42)(89, \"a\", 7);\n          i0.ɵɵelement(90, \"i\", 8);\n          i0.ɵɵtext(91, \"Create Account \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(92, \"a\", 43);\n          i0.ɵɵelement(93, \"i\", 44);\n          i0.ɵɵtext(94, \"Admin Panel \");\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(61);\n          i0.ɵɵproperty(\"ngForOf\", ctx.categories);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.categories.length === 0);\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.loading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.loading);\n        }\n      },\n      dependencies: [CommonModule, i2.NgForOf, i2.NgIf, i2.DecimalPipe, RouterModule, i3.RouterLink],\n      styles: [\".hero-section[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  padding: 4rem 0;\\n  min-height: 60vh;\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.hero-icon[_ngcontent-%COMP%] {\\n  font-size: 15rem;\\n  opacity: 0.3;\\n  color: white;\\n}\\n\\n.feature-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n}\\n\\n.product-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n}\\n\\n.category-badge[_ngcontent-%COMP%] {\\n  background: #f8f9fa;\\n  border: 1px solid #dee2e6;\\n  border-radius: 20px;\\n  padding: 8px 16px;\\n  margin: 4px;\\n  display: inline-block;\\n  color: #495057;\\n  transition: all 0.3s ease;\\n  cursor: pointer;\\n}\\n\\n.category-badge[_ngcontent-%COMP%]:hover {\\n  background: #e9ecef;\\n  transform: translateY(-2px);\\n}\\n\\n.category-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  justify-content: center;\\n  gap: 8px;\\n}\\n\\n@media (max-width: 768px) {\\n  .hero-section[_ngcontent-%COMP%] {\\n    padding: 2rem 0;\\n    text-align: center;\\n  }\\n  .hero-icon[_ngcontent-%COMP%] {\\n    font-size: 8rem;\\n    margin-top: 2rem;\\n  }\\n  .display-4[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  .btn-lg[_ngcontent-%COMP%] {\\n    width: 100%;\\n    margin-bottom: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "category_r1", "ɵɵclassProp", "i_r3", "ɵɵtextInterpolate", "product_r2", "productName", "ɵɵpipeBind2", "price", "category", "quantity", "ɵɵelementContainerStart", "ɵɵtemplate", "HomeComponent_div_77_div_1_Template", "HomeComponent_div_77_ng_container_2_Template", "ɵɵproperty", "ctx_r3", "featuredProducts", "length", "HomeComponent", "constructor", "productService", "categories", "loading", "ngOnInit", "loadFeaturedProducts", "loadCategories", "getInStockProducts", "subscribe", "next", "products", "slice", "error", "console", "getAllCategories", "ɵɵdirectiveInject", "i1", "ProductService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "HomeComponent_Template", "rf", "ctx", "HomeComponent_span_61_Template", "HomeComponent_span_62_Template", "HomeComponent_span_63_Template", "HomeComponent_span_64_Template", "HomeComponent_span_65_Template", "HomeComponent_span_66_Template", "HomeComponent_span_67_Template", "HomeComponent_div_76_Template", "HomeComponent_div_77_Template", "i2", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "NgIf", "DecimalPipe", "i3", "RouterLink", "styles"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/components/home/<USER>"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ProductService } from '../../services/product.service';\nimport { Product } from '../../models/product.model';\n\n/**\n * Home page component displaying featured products and categories.\n * \n * <AUTHOR>\n * @version 1.0.0\n */\n@Component({\n  selector: 'app-home',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <!-- Hero Section -->\n    <section class=\"hero-section\">\n      <div class=\"container\">\n        <div class=\"row align-items-center\">\n          <div class=\"col-lg-6\">\n            <h1 class=\"display-4 fw-bold mb-4 text-white\">\n              Fresh Groceries Delivered to Your Door\n            </h1>\n            <p class=\"lead mb-4 text-light\">\n              Shop from our wide selection of fresh fruits, vegetables, dairy products, and more. \n              Get everything you need with just a few clicks!\n            </p>\n            <div class=\"d-flex gap-3 flex-wrap\">\n              <a routerLink=\"/register\" class=\"btn btn-light btn-lg\">\n                <i class=\"fas fa-user-plus me-2\"></i>Get Started\n              </a>\n              <a routerLink=\"/products/search\" class=\"btn btn-outline-light btn-lg\">\n                <i class=\"fas fa-search me-2\"></i>Browse Products\n              </a>\n            </div>\n          </div>\n          <div class=\"col-lg-6 text-center\">\n            <i class=\"fas fa-shopping-basket hero-icon\"></i>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Features Section -->\n    <section class=\"py-5\">\n      <div class=\"container\">\n        <div class=\"row text-center mb-5\">\n          <div class=\"col\">\n            <h2 class=\"fw-bold\">Why Choose FreshMart?</h2>\n            <p class=\"text-muted\">Experience the best online grocery shopping</p>\n          </div>\n        </div>\n        <div class=\"row\">\n          <div class=\"col-md-4 mb-4\">\n            <div class=\"card card-custom h-100 text-center p-4\">\n              <div class=\"card-body\">\n                <i class=\"fas fa-leaf text-success mb-3 feature-icon\"></i>\n                <h5 class=\"card-title\">Fresh & Organic</h5>\n                <p class=\"card-text\">\n                  Hand-picked fresh produce and organic products delivered straight from farms to your table.\n                </p>\n              </div>\n            </div>\n          </div>\n          <div class=\"col-md-4 mb-4\">\n            <div class=\"card card-custom h-100 text-center p-4\">\n              <div class=\"card-body\">\n                <i class=\"fas fa-truck text-primary mb-3 feature-icon\"></i>\n                <h5 class=\"card-title\">Fast Delivery</h5>\n                <p class=\"card-text\">\n                  Quick and reliable delivery service. Get your groceries delivered within hours of ordering.\n                </p>\n              </div>\n            </div>\n          </div>\n          <div class=\"col-md-4 mb-4\">\n            <div class=\"card card-custom h-100 text-center p-4\">\n              <div class=\"card-body\">\n                <i class=\"fas fa-shield-alt text-warning mb-3 feature-icon\"></i>\n                <h5 class=\"card-title\">Secure Shopping</h5>\n                <p class=\"card-text\">\n                  Safe and secure payment processing with advanced encryption to protect your data.\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Categories Section -->\n    <section class=\"py-5 bg-light\">\n      <div class=\"container\">\n        <div class=\"row text-center mb-5\">\n          <div class=\"col\">\n            <h2 class=\"fw-bold\">Shop by Category</h2>\n            <p class=\"text-muted\">Explore our wide range of product categories</p>\n          </div>\n        </div>\n        <div class=\"row justify-content-center\">\n          <div class=\"col-md-8 text-center\">\n            <div class=\"category-container\">\n              <span *ngFor=\"let category of categories\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>{{ category }}\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Fruits\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Vegetables\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Dairy\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Bakery\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Meat\n              </span>\n              <span *ngIf=\"categories.length === 0\" class=\"category-badge\">\n                <i class=\"fas fa-tag me-1\"></i>Grains\n              </span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n\n    <!-- Featured Products Section -->\n    <section class=\"py-5\">\n      <div class=\"container\">\n        <div class=\"row text-center mb-5\">\n          <div class=\"col\">\n            <h2 class=\"fw-bold\">Featured Products</h2>\n            <p class=\"text-muted\">Check out our most popular items</p>\n          </div>\n        </div>\n        \n        <!-- Loading State -->\n        <div *ngIf=\"loading\" class=\"text-center\">\n          <div class=\"spinner-custom mx-auto\"></div>\n          <p class=\"mt-3\">Loading products...</p>\n        </div>\n        \n        <!-- Products Grid -->\n        <div *ngIf=\"!loading\" class=\"row\">\n          <div *ngFor=\"let product of featuredProducts; let i = index\" \n               class=\"col-md-3 col-sm-6 mb-4\" \n               [class.d-none]=\"i >= 8\">\n            <div class=\"card product-card h-100\">\n              <div class=\"card-body text-center\">\n                <i class=\"fas fa-apple-alt text-success mb-3 product-icon\"></i>\n                <h6 class=\"card-title\">{{ product.productName }}</h6>\n                <p class=\"product-price\">₹{{ product.price | number:'1.2-2' }}</p>\n                <p class=\"text-muted small\">{{ product.category || 'General' }}</p>\n                <span class=\"badge\" \n                      [class.bg-success]=\"product.quantity > 0\"\n                      [class.bg-danger]=\"product.quantity <= 0\">\n                  {{ product.quantity > 0 ? 'In Stock' : 'Out of Stock' }}\n                </span>\n              </div>\n            </div>\n          </div>\n          \n          <!-- Default Products if none loaded -->\n          <ng-container *ngIf=\"featuredProducts.length === 0\">\n            <div class=\"col-md-3 col-sm-6 mb-4\">\n              <div class=\"card product-card h-100\">\n                <div class=\"card-body text-center\">\n                  <i class=\"fas fa-apple-alt text-success mb-3 product-icon\"></i>\n                  <h6 class=\"card-title\">Fresh Apples</h6>\n                  <p class=\"product-price\">₹150.00</p>\n                  <p class=\"text-muted small\">Fruits</p>\n                  <span class=\"badge bg-success\">In Stock</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 col-sm-6 mb-4\">\n              <div class=\"card product-card h-100\">\n                <div class=\"card-body text-center\">\n                  <i class=\"fas fa-seedling text-warning mb-3 product-icon\"></i>\n                  <h6 class=\"card-title\">Fresh Bananas</h6>\n                  <p class=\"product-price\">₹80.00</p>\n                  <p class=\"text-muted small\">Fruits</p>\n                  <span class=\"badge bg-success\">In Stock</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 col-sm-6 mb-4\">\n              <div class=\"card product-card h-100\">\n                <div class=\"card-body text-center\">\n                  <i class=\"fas fa-cheese text-warning mb-3 product-icon\"></i>\n                  <h6 class=\"card-title\">Fresh Milk</h6>\n                  <p class=\"product-price\">₹60.00</p>\n                  <p class=\"text-muted small\">Dairy</p>\n                  <span class=\"badge bg-success\">In Stock</span>\n                </div>\n              </div>\n            </div>\n            <div class=\"col-md-3 col-sm-6 mb-4\">\n              <div class=\"card product-card h-100\">\n                <div class=\"card-body text-center\">\n                  <i class=\"fas fa-bread-slice text-warning mb-3 product-icon\"></i>\n                  <h6 class=\"card-title\">Whole Wheat Bread</h6>\n                  <p class=\"product-price\">₹40.00</p>\n                  <p class=\"text-muted small\">Bakery</p>\n                  <span class=\"badge bg-success\">In Stock</span>\n                </div>\n              </div>\n            </div>\n          </ng-container>\n        </div>\n        \n        <div class=\"text-center mt-4\">\n          <a routerLink=\"/products\" class=\"btn btn-primary-custom btn-lg\">\n            <i class=\"fas fa-eye me-2\"></i>View All Products\n          </a>\n        </div>\n      </div>\n    </section>\n\n    <!-- CTA Section -->\n    <section class=\"py-5 bg-primary-custom text-white\">\n      <div class=\"container text-center\">\n        <h2 class=\"fw-bold mb-4\">Ready to Start Shopping?</h2>\n        <p class=\"lead mb-4\">\n          Join thousands of satisfied customers who trust FreshMart for their grocery needs.\n        </p>\n        <div class=\"d-flex justify-content-center gap-3 flex-wrap\">\n          <a routerLink=\"/register\" class=\"btn btn-light btn-lg\">\n            <i class=\"fas fa-user-plus me-2\"></i>Create Account\n          </a>\n          <a routerLink=\"/admin/login\" class=\"btn btn-outline-light btn-lg\">\n            <i class=\"fas fa-cogs me-2\"></i>Admin Panel\n          </a>\n        </div>\n      </div>\n    </section>\n  `,\n  styles: [`\n    .hero-section {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      padding: 4rem 0;\n      min-height: 60vh;\n      display: flex;\n      align-items: center;\n    }\n    \n    .hero-icon {\n      font-size: 15rem;\n      opacity: 0.3;\n      color: white;\n    }\n    \n    .feature-icon {\n      font-size: 3rem;\n    }\n    \n    .product-icon {\n      font-size: 3rem;\n    }\n    \n    .category-badge {\n      background: #f8f9fa;\n      border: 1px solid #dee2e6;\n      border-radius: 20px;\n      padding: 8px 16px;\n      margin: 4px;\n      display: inline-block;\n      color: #495057;\n      transition: all 0.3s ease;\n      cursor: pointer;\n    }\n    \n    .category-badge:hover {\n      background: #e9ecef;\n      transform: translateY(-2px);\n    }\n    \n    .category-container {\n      display: flex;\n      flex-wrap: wrap;\n      justify-content: center;\n      gap: 8px;\n    }\n    \n    @media (max-width: 768px) {\n      .hero-section {\n        padding: 2rem 0;\n        text-align: center;\n      }\n      \n      .hero-icon {\n        font-size: 8rem;\n        margin-top: 2rem;\n      }\n      \n      .display-4 {\n        font-size: 2rem;\n      }\n      \n      .btn-lg {\n        width: 100%;\n        margin-bottom: 1rem;\n      }\n    }\n  `]\n})\nexport class HomeComponent implements OnInit {\n  featuredProducts: Product[] = [];\n  categories: string[] = [];\n  loading = true;\n\n  constructor(private productService: ProductService) {}\n\n  ngOnInit(): void {\n    this.loadFeaturedProducts();\n    this.loadCategories();\n  }\n\n  private loadFeaturedProducts(): void {\n    this.productService.getInStockProducts().subscribe({\n      next: (products) => {\n        this.featuredProducts = products.slice(0, 8); // Show only first 8 products\n        this.loading = false;\n      },\n      error: (error) => {\n        console.error('Error loading featured products:', error);\n        this.loading = false;\n      }\n    });\n  }\n\n  private loadCategories(): void {\n    this.productService.getAllCategories().subscribe({\n      next: (categories) => {\n        this.categories = categories;\n      },\n      error: (error) => {\n        console.error('Error loading categories:', error);\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;;;;;IAsGhCC,EAAA,CAAAC,cAAA,eAAiE;IAC/DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,GACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;IAD0BJ,EAAA,CAAAK,SAAA,GACjC;IADiCL,EAAA,CAAAM,kBAAA,KAAAC,WAAA,MACjC;;;;;IACAP,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,cACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,kBACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,aACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,cACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,YACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IACPJ,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAE,SAAA,YAA+B;IAAAF,EAAA,CAAAG,MAAA,cACjC;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAkBbJ,EAAA,CAAAC,cAAA,cAAyC;IACvCD,EAAA,CAAAE,SAAA,cAA0C;IAC1CF,EAAA,CAAAC,cAAA,YAAgB;IAAAD,EAAA,CAAAG,MAAA,0BAAmB;IACrCH,EADqC,CAAAI,YAAA,EAAI,EACnC;;;;;IAQAJ,EAJJ,CAAAC,cAAA,cAE6B,cACU,cACA;IACjCD,EAAA,CAAAE,SAAA,YAA+D;IAC/DF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,GAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrDJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,GAAqC;;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAClEJ,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,IAAmC;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnEJ,EAAA,CAAAC,cAAA,gBAEgD;IAC9CD,EAAA,CAAAG,MAAA,IACF;IAGNH,EAHM,CAAAI,YAAA,EAAO,EACH,EACF,EACF;;;;;IAdDJ,EAAA,CAAAQ,WAAA,WAAAC,IAAA,MAAuB;IAICT,EAAA,CAAAK,SAAA,GAAyB;IAAzBL,EAAA,CAAAU,iBAAA,CAAAC,UAAA,CAAAC,WAAA,CAAyB;IACvBZ,EAAA,CAAAK,SAAA,GAAqC;IAArCL,EAAA,CAAAM,kBAAA,WAAAN,EAAA,CAAAa,WAAA,QAAAF,UAAA,CAAAG,KAAA,eAAqC;IAClCd,EAAA,CAAAK,SAAA,GAAmC;IAAnCL,EAAA,CAAAU,iBAAA,CAAAC,UAAA,CAAAI,QAAA,cAAmC;IAEzDf,EAAA,CAAAK,SAAA,EAAyC;IACzCL,EADA,CAAAQ,WAAA,eAAAG,UAAA,CAAAK,QAAA,KAAyC,cAAAL,UAAA,CAAAK,QAAA,MACA;IAC7ChB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAM,kBAAA,MAAAK,UAAA,CAAAK,QAAA,wCACF;;;;;IAMNhB,EAAA,CAAAiB,uBAAA,GAAoD;IAG9CjB,EAFJ,CAAAC,cAAA,cAAoC,cACG,cACA;IACjCD,EAAA,CAAAE,SAAA,YAA+D;IAC/DF,EAAA,CAAAC,cAAA,aAAuB;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACxCJ,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAO;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACpCJ,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtCJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAG7CH,EAH6C,CAAAI,YAAA,EAAO,EAC1C,EACF,EACF;IAGFJ,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACA;IACjCD,EAAA,CAAAE,SAAA,aAA8D;IAC9DF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACzCJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnCJ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtCJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAG7CH,EAH6C,CAAAI,YAAA,EAAO,EAC1C,EACF,EACF;IAGFJ,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACA;IACjCD,EAAA,CAAAE,SAAA,aAA4D;IAC5DF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACtCJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnCJ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACrCJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAG7CH,EAH6C,CAAAI,YAAA,EAAO,EAC1C,EACF,EACF;IAGFJ,EAFJ,CAAAC,cAAA,eAAoC,eACG,eACA;IACjCD,EAAA,CAAAE,SAAA,aAAiE;IACjEF,EAAA,CAAAC,cAAA,cAAuB;IAAAD,EAAA,CAAAG,MAAA,yBAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC7CJ,EAAA,CAAAC,cAAA,aAAyB;IAAAD,EAAA,CAAAG,MAAA,mBAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACnCJ,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAAAH,EAAA,CAAAI,YAAA,EAAI;IACtCJ,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAG,MAAA,gBAAQ;IAG7CH,EAH6C,CAAAI,YAAA,EAAO,EAC1C,EACF,EACF;;;;;;IAhEVJ,EAAA,CAAAC,cAAA,cAAkC;IAoBhCD,EAnBA,CAAAkB,UAAA,IAAAC,mCAAA,oBAE6B,IAAAC,4CAAA,4BAiBuB;IA8CtDpB,EAAA,CAAAI,YAAA,EAAM;;;;IAjEqBJ,EAAA,CAAAK,SAAA,EAAqB;IAArBL,EAAA,CAAAqB,UAAA,YAAAC,MAAA,CAAAC,gBAAA,CAAqB;IAmB/BvB,EAAA,CAAAK,SAAA,EAAmC;IAAnCL,EAAA,CAAAqB,UAAA,SAAAC,MAAA,CAAAC,gBAAA,CAAAC,MAAA,OAAmC;;;AAlK5D;;;;;;AAiTA,OAAM,MAAOC,aAAa;EAKxBC,YAAoBC,cAA8B;IAA9B,KAAAA,cAAc,GAAdA,cAAc;IAJlC,KAAAJ,gBAAgB,GAAc,EAAE;IAChC,KAAAK,UAAU,GAAa,EAAE;IACzB,KAAAC,OAAO,GAAG,IAAI;EAEuC;EAErDC,QAAQA,CAAA;IACN,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,cAAc,EAAE;EACvB;EAEQD,oBAAoBA,CAAA;IAC1B,IAAI,CAACJ,cAAc,CAACM,kBAAkB,EAAE,CAACC,SAAS,CAAC;MACjDC,IAAI,EAAGC,QAAQ,IAAI;QACjB,IAAI,CAACb,gBAAgB,GAAGa,QAAQ,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC9C,IAAI,CAACR,OAAO,GAAG,KAAK;MACtB,CAAC;MACDS,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;QACxD,IAAI,CAACT,OAAO,GAAG,KAAK;MACtB;KACD,CAAC;EACJ;EAEQG,cAAcA,CAAA;IACpB,IAAI,CAACL,cAAc,CAACa,gBAAgB,EAAE,CAACN,SAAS,CAAC;MAC/CC,IAAI,EAAGP,UAAU,IAAI;QACnB,IAAI,CAACA,UAAU,GAAGA,UAAU;MAC9B,CAAC;MACDU,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACnD;KACD,CAAC;EACJ;;;uBAlCWb,aAAa,EAAAzB,EAAA,CAAAyC,iBAAA,CAAAC,EAAA,CAAAC,cAAA;IAAA;EAAA;;;YAAblB,aAAa;MAAAmB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA9C,EAAA,CAAA+C,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,uBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjSdrD,EAJR,CAAAC,cAAA,iBAA8B,aACL,aACe,aACZ,YAC0B;UAC5CD,EAAA,CAAAG,MAAA,+CACF;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACLJ,EAAA,CAAAC,cAAA,WAAgC;UAC9BD,EAAA,CAAAG,MAAA,4IAEF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAEFJ,EADF,CAAAC,cAAA,aAAoC,WACqB;UACrDD,EAAA,CAAAE,SAAA,YAAqC;UAAAF,EAAA,CAAAG,MAAA,oBACvC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,YAAsE;UACpED,EAAA,CAAAE,SAAA,aAAkC;UAAAF,EAAA,CAAAG,MAAA,wBACpC;UAEJH,EAFI,CAAAI,YAAA,EAAI,EACA,EACF;UACNJ,EAAA,CAAAC,cAAA,eAAkC;UAChCD,EAAA,CAAAE,SAAA,aAAgD;UAIxDF,EAHM,CAAAI,YAAA,EAAM,EACF,EACF,EACE;UAOFJ,EAJR,CAAAC,cAAA,mBAAsB,cACG,eACa,eACf,cACK;UAAAD,EAAA,CAAAG,MAAA,6BAAqB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC9CJ,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAG,MAAA,mDAA2C;UAErEH,EAFqE,CAAAI,YAAA,EAAI,EACjE,EACF;UAIAJ,EAHN,CAAAC,cAAA,eAAiB,eACY,eAC2B,eAC3B;UACrBD,EAAA,CAAAE,SAAA,aAA0D;UAC1DF,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3CJ,EAAA,CAAAC,cAAA,aAAqB;UACnBD,EAAA,CAAAG,MAAA,qGACF;UAGNH,EAHM,CAAAI,YAAA,EAAI,EACA,EACF,EACF;UAGFJ,EAFJ,CAAAC,cAAA,eAA2B,eAC2B,eAC3B;UACrBD,EAAA,CAAAE,SAAA,aAA2D;UAC3DF,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzCJ,EAAA,CAAAC,cAAA,aAAqB;UACnBD,EAAA,CAAAG,MAAA,qGACF;UAGNH,EAHM,CAAAI,YAAA,EAAI,EACA,EACF,EACF;UAGFJ,EAFJ,CAAAC,cAAA,eAA2B,eAC2B,eAC3B;UACrBD,EAAA,CAAAE,SAAA,aAAgE;UAChEF,EAAA,CAAAC,cAAA,cAAuB;UAAAD,EAAA,CAAAG,MAAA,uBAAe;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC3CJ,EAAA,CAAAC,cAAA,aAAqB;UACnBD,EAAA,CAAAG,MAAA,2FACF;UAMZH,EANY,CAAAI,YAAA,EAAI,EACA,EACF,EACF,EACF,EACF,EACE;UAOFJ,EAJR,CAAAC,cAAA,mBAA+B,cACN,eACa,eACf,cACK;UAAAD,EAAA,CAAAG,MAAA,wBAAgB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACzCJ,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAG,MAAA,oDAA4C;UAEtEH,EAFsE,CAAAI,YAAA,EAAI,EAClE,EACF;UAGFJ,EAFJ,CAAAC,cAAA,eAAwC,eACJ,eACA;UAmB9BD,EAlBA,CAAAkB,UAAA,KAAAqC,8BAAA,mBAAiE,KAAAC,8BAAA,mBAGJ,KAAAC,8BAAA,mBAGA,KAAAC,8BAAA,mBAGA,KAAAC,8BAAA,mBAGA,KAAAC,8BAAA,mBAGA,KAAAC,8BAAA,mBAGA;UAOvE7D,EAJQ,CAAAI,YAAA,EAAM,EACF,EACF,EACF,EACE;UAOFJ,EAJR,CAAAC,cAAA,mBAAsB,cACG,eACa,eACf,cACK;UAAAD,EAAA,CAAAG,MAAA,yBAAiB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAC1CJ,EAAA,CAAAC,cAAA,aAAsB;UAAAD,EAAA,CAAAG,MAAA,wCAAgC;UAE1DH,EAF0D,CAAAI,YAAA,EAAI,EACtD,EACF;UASNJ,EANA,CAAAkB,UAAA,KAAA4C,6BAAA,kBAAyC,KAAAC,6BAAA,kBAMP;UAqEhC/D,EADF,CAAAC,cAAA,eAA8B,aACoC;UAC9DD,EAAA,CAAAE,SAAA,aAA+B;UAAAF,EAAA,CAAAG,MAAA,0BACjC;UAGNH,EAHM,CAAAI,YAAA,EAAI,EACA,EACF,EACE;UAKNJ,EAFJ,CAAAC,cAAA,mBAAmD,eACd,cACR;UAAAD,EAAA,CAAAG,MAAA,gCAAwB;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACtDJ,EAAA,CAAAC,cAAA,aAAqB;UACnBD,EAAA,CAAAG,MAAA,4FACF;UAAAH,EAAA,CAAAI,YAAA,EAAI;UAEFJ,EADF,CAAAC,cAAA,eAA2D,YACF;UACrDD,EAAA,CAAAE,SAAA,YAAqC;UAAAF,EAAA,CAAAG,MAAA,uBACvC;UAAAH,EAAA,CAAAI,YAAA,EAAI;UACJJ,EAAA,CAAAC,cAAA,aAAkE;UAChED,EAAA,CAAAE,SAAA,aAAgC;UAAAF,EAAA,CAAAG,MAAA,oBAClC;UAGNH,EAHM,CAAAI,YAAA,EAAI,EACA,EACF,EACE;;;UAxI2BJ,EAAA,CAAAK,SAAA,IAAa;UAAbL,EAAA,CAAAqB,UAAA,YAAAiC,GAAA,CAAA1B,UAAA,CAAa;UAGjC5B,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAG7BxB,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAG7BxB,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAG7BxB,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAG7BxB,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAG7BxB,EAAA,CAAAK,SAAA,EAA6B;UAA7BL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAA1B,UAAA,CAAAJ,MAAA,OAA6B;UAoBpCxB,EAAA,CAAAK,SAAA,GAAa;UAAbL,EAAA,CAAAqB,UAAA,SAAAiC,GAAA,CAAAzB,OAAA,CAAa;UAMb7B,EAAA,CAAAK,SAAA,EAAc;UAAdL,EAAA,CAAAqB,UAAA,UAAAiC,GAAA,CAAAzB,OAAA,CAAc;;;qBArIhB/B,YAAY,EAAAkE,EAAA,CAAAC,OAAA,EAAAD,EAAA,CAAAE,IAAA,EAAAF,EAAA,CAAAG,WAAA,EAAEpE,YAAY,EAAAqE,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}