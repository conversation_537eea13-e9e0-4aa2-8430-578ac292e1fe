{"ast": null, "code": "import { importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\nimport { routes } from './app.routes';\nexport const appConfig = {\n  providers: [provideRouter(routes), provideHttpClient(withInterceptorsFromDi()), provideAnimations(), importProvidersFrom(FormsModule, ReactiveFormsModule)]\n};", "map": {"version": 3, "names": ["importProvidersFrom", "provideRouter", "provideHttpClient", "withInterceptorsFromDi", "provideAnimations", "FormsModule", "ReactiveFormsModule", "routes", "appConfig", "providers"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/app.config.ts"], "sourcesContent": ["import { ApplicationConfig, importProvidersFrom } from '@angular/core';\nimport { provideRouter } from '@angular/router';\nimport { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';\nimport { provideAnimations } from '@angular/platform-browser/animations';\nimport { FormsModule, ReactiveFormsModule } from '@angular/forms';\n\nimport { routes } from './app.routes';\n\nexport const appConfig: ApplicationConfig = {\n  providers: [\n    provideRouter(routes),\n    provideHttpClient(withInterceptorsFromDi()),\n    provideAnimations(),\n    importProvidersFrom(FormsModule, ReactiveFormsModule)\n  ]\n};\n"], "mappings": "AAAA,SAA4BA,mBAAmB,QAAQ,eAAe;AACtE,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,iBAAiB,EAAEC,sBAAsB,QAAQ,sBAAsB;AAChF,SAASC,iBAAiB,QAAQ,sCAAsC;AACxE,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,gBAAgB;AAEjE,SAASC,MAAM,QAAQ,cAAc;AAErC,OAAO,MAAMC,SAAS,GAAsB;EAC1CC,SAAS,EAAE,CACTR,aAAa,CAACM,MAAM,CAAC,EACrBL,iBAAiB,CAACC,sBAAsB,EAAE,CAAC,EAC3CC,iBAAiB,EAAE,EACnBJ,mBAAmB,CAACK,WAAW,EAAEC,mBAAmB,CAAC;CAExD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}