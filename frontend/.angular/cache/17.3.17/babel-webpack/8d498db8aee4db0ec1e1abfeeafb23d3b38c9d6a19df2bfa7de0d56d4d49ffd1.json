{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = () => ({\n  exact: true\n});\nfunction NavbarComponent_ng_container_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 8)(2, \"a\", 17);\n    i0.ɵɵelement(3, \"i\", 18);\n    i0.ɵɵtext(4, \"Login \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 8)(6, \"a\", 19);\n    i0.ɵɵelement(7, \"i\", 20);\n    i0.ɵɵtext(8, \"Register \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 8)(10, \"a\", 21);\n    i0.ɵɵelement(11, \"i\", 22);\n    i0.ɵɵtext(12, \"Admin \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NavbarComponent_ng_container_23_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 8)(2, \"a\", 31);\n    i0.ɵɵelement(3, \"i\", 32);\n    i0.ɵɵtext(4, \"Dashboard \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 8)(6, \"a\", 33);\n    i0.ɵɵelement(7, \"i\", 34);\n    i0.ɵɵtext(8, \"My Orders \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"li\", 8)(10, \"a\", 35);\n    i0.ɵɵelement(11, \"i\", 36);\n    i0.ɵɵtext(12, \"Profile \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NavbarComponent_ng_container_23_ng_container_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"li\", 8)(2, \"a\", 37);\n    i0.ɵɵelement(3, \"i\", 32);\n    i0.ɵɵtext(4, \"Dashboard \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"li\", 23)(6, \"a\", 38);\n    i0.ɵɵelement(7, \"i\", 39);\n    i0.ɵɵtext(8, \"Manage \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"ul\", 40)(10, \"li\")(11, \"a\", 41);\n    i0.ɵɵelement(12, \"i\", 42);\n    i0.ɵɵtext(13, \"Customers \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"li\")(15, \"a\", 43);\n    i0.ɵɵelement(16, \"i\", 44);\n    i0.ɵɵtext(17, \"Products \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"li\")(19, \"a\", 45);\n    i0.ɵɵelement(20, \"i\", 3);\n    i0.ɵɵtext(21, \"Orders \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction NavbarComponent_ng_container_23_li_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"li\")(1, \"a\", 46);\n    i0.ɵɵelement(2, \"i\", 47);\n    i0.ɵɵtext(3, \"Profile \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction NavbarComponent_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtemplate(1, NavbarComponent_ng_container_23_ng_container_1_Template, 13, 0, \"ng-container\", 16)(2, NavbarComponent_ng_container_23_ng_container_2_Template, 22, 0, \"ng-container\", 16);\n    i0.ɵɵelementStart(3, \"li\", 23)(4, \"a\", 24);\n    i0.ɵɵelement(5, \"i\", 25);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"ul\", 26)(8, \"li\")(9, \"h6\", 27);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(11, \"li\");\n    i0.ɵɵelement(12, \"hr\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, NavbarComponent_ng_container_23_li_13_Template, 4, 0, \"li\", 16);\n    i0.ɵɵelementStart(14, \"li\")(15, \"a\", 29);\n    i0.ɵɵlistener(\"click\", function NavbarComponent_ng_container_23_Template_a_click_15_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout($event));\n    });\n    i0.ɵɵelement(16, \"i\", 30);\n    i0.ɵɵtext(17, \"Logout \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isCustomer());\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isAdmin());\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.username, \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.currentUser.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.authService.isCustomer());\n  }\n}\n/**\n * Navigation bar component for the application.\n *\n * <AUTHOR> Singhal\n * @version 1.0.0\n */\nexport class NavbarComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.isAuthenticated = false;\n    this.currentUser = null;\n    this.subscriptions = [];\n  }\n  ngOnInit() {\n    // Subscribe to authentication status\n    this.subscriptions.push(this.authService.isAuthenticated$.subscribe(isAuth => this.isAuthenticated = isAuth));\n    // Subscribe to current user\n    this.subscriptions.push(this.authService.currentUser$.subscribe(user => this.currentUser = user));\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  logout(event) {\n    event.preventDefault();\n    this.authService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/']);\n      },\n      error: error => {\n        console.error('Logout error:', error);\n        // Even if logout fails on server, clear local session\n        this.router.navigate(['/']);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function NavbarComponent_Factory(t) {\n      return new (t || NavbarComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NavbarComponent,\n      selectors: [[\"app-navbar\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 24,\n      vars: 4,\n      consts: [[1, \"navbar\", \"navbar-expand-lg\", \"navbar-dark\", \"navbar-custom\"], [1, \"container\"], [\"routerLink\", \"/\", 1, \"navbar-brand\", \"navbar-brand-custom\"], [1, \"fas\", \"fa-shopping-cart\", \"me-2\"], [\"type\", \"button\", \"data-bs-toggle\", \"collapse\", \"data-bs-target\", \"#navbarNav\", \"aria-controls\", \"navbarNav\", \"aria-expanded\", \"false\", \"aria-label\", \"Toggle navigation\", 1, \"navbar-toggler\"], [1, \"navbar-toggler-icon\"], [\"id\", \"navbarNav\", 1, \"collapse\", \"navbar-collapse\"], [1, \"navbar-nav\", \"me-auto\"], [1, \"nav-item\"], [\"routerLink\", \"/\", \"routerLinkActive\", \"active\", 1, \"nav-link\", 3, \"routerLinkActiveOptions\"], [1, \"fas\", \"fa-home\", \"me-1\"], [\"routerLink\", \"/products\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-shopping-basket\", \"me-1\"], [\"routerLink\", \"/products/search\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-search\", \"me-1\"], [1, \"navbar-nav\"], [4, \"ngIf\"], [\"routerLink\", \"/login\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-sign-in-alt\", \"me-1\"], [\"routerLink\", \"/register\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-user-plus\", \"me-1\"], [\"routerLink\", \"/admin/login\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-user-shield\", \"me-1\"], [1, \"nav-item\", \"dropdown\"], [\"href\", \"#\", \"id\", \"userDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"fas\", \"fa-user-circle\", \"me-1\"], [\"aria-labelledby\", \"userDropdown\", 1, \"dropdown-menu\", \"dropdown-menu-end\"], [1, \"dropdown-header\"], [1, \"dropdown-divider\"], [\"href\", \"#\", 1, \"dropdown-item\", 3, \"click\"], [1, \"fas\", \"fa-sign-out-alt\", \"me-2\"], [\"routerLink\", \"/customer/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-tachometer-alt\", \"me-1\"], [\"routerLink\", \"/customer/orders\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-shopping-bag\", \"me-1\"], [\"routerLink\", \"/customer/profile\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [1, \"fas\", \"fa-user\", \"me-1\"], [\"routerLink\", \"/admin/dashboard\", \"routerLinkActive\", \"active\", 1, \"nav-link\"], [\"href\", \"#\", \"id\", \"adminDropdown\", \"role\", \"button\", \"data-bs-toggle\", \"dropdown\", \"aria-expanded\", \"false\", 1, \"nav-link\", \"dropdown-toggle\"], [1, \"fas\", \"fa-cogs\", \"me-1\"], [\"aria-labelledby\", \"adminDropdown\", 1, \"dropdown-menu\"], [\"routerLink\", \"/admin/customers\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-users\", \"me-2\"], [\"routerLink\", \"/admin/products\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-box\", \"me-2\"], [\"routerLink\", \"/admin/orders\", 1, \"dropdown-item\"], [\"routerLink\", \"/customer/profile\", 1, \"dropdown-item\"], [1, \"fas\", \"fa-user\", \"me-2\"]],\n      template: function NavbarComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"nav\", 0)(1, \"div\", 1)(2, \"a\", 2);\n          i0.ɵɵelement(3, \"i\", 3);\n          i0.ɵɵtext(4, \"FreshMart \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"button\", 4);\n          i0.ɵɵelement(6, \"span\", 5);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"div\", 6)(8, \"ul\", 7)(9, \"li\", 8)(10, \"a\", 9);\n          i0.ɵɵelement(11, \"i\", 10);\n          i0.ɵɵtext(12, \"Home \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(13, \"li\", 8)(14, \"a\", 11);\n          i0.ɵɵelement(15, \"i\", 12);\n          i0.ɵɵtext(16, \"Products \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"li\", 8)(18, \"a\", 13);\n          i0.ɵɵelement(19, \"i\", 14);\n          i0.ɵɵtext(20, \"Search \");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(21, \"ul\", 15);\n          i0.ɵɵtemplate(22, NavbarComponent_ng_container_22_Template, 13, 0, \"ng-container\", 16)(23, NavbarComponent_ng_container_23_Template, 18, 5, \"ng-container\", 16);\n          i0.ɵɵelementEnd()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"routerLinkActiveOptions\", i0.ɵɵpureFunction0(3, _c0));\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngIf\", !ctx.isAuthenticated);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isAuthenticated && ctx.currentUser);\n        }\n      },\n      dependencies: [CommonModule, i3.NgIf, RouterModule, i2.RouterLink, i2.RouterLinkActive],\n      styles: [\".navbar-custom[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\\n}\\n\\n.navbar-brand-custom[_ngcontent-%COMP%] {\\n  font-weight: bold;\\n  font-size: 1.5rem;\\n  color: white !important;\\n}\\n\\n.nav-link[_ngcontent-%COMP%] {\\n  color: rgba(255, 255, 255, 0.9) !important;\\n  transition: color 0.3s ease;\\n}\\n\\n.nav-link[_ngcontent-%COMP%]:hover, .nav-link.active[_ngcontent-%COMP%] {\\n  color: white !important;\\n}\\n\\n.dropdown-menu[_ngcontent-%COMP%] {\\n  border: none;\\n  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%] {\\n  transition: background-color 0.3s ease;\\n}\\n\\n.dropdown-item[_ngcontent-%COMP%]:hover {\\n  background-color: #f8f9fa;\\n}\\n\\n@media (max-width: 991px) {\\n  .navbar-nav[_ngcontent-%COMP%] {\\n    text-align: center;\\n  }\\n  .dropdown-menu[_ngcontent-%COMP%] {\\n    position: static !important;\\n    float: none;\\n    width: auto;\\n    margin-top: 0;\\n    background-color: transparent;\\n    border: 0;\\n    box-shadow: none;\\n  }\\n  .dropdown-item[_ngcontent-%COMP%] {\\n    color: rgba(255, 255, 255, 0.9) !important;\\n  }\\n  .dropdown-item[_ngcontent-%COMP%]:hover {\\n    background-color: rgba(255, 255, 255, 0.1);\\n    color: white !important;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵtemplate", "NavbarComponent_ng_container_23_ng_container_1_Template", "NavbarComponent_ng_container_23_ng_container_2_Template", "NavbarComponent_ng_container_23_li_13_Template", "ɵɵlistener", "NavbarComponent_ng_container_23_Template_a_click_15_listener", "$event", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "logout", "ɵɵadvance", "ɵɵproperty", "authService", "isCustomer", "isAdmin", "ɵɵtextInterpolate1", "currentUser", "username", "ɵɵtextInterpolate", "email", "NavbarComponent", "constructor", "router", "isAuthenticated", "subscriptions", "ngOnInit", "push", "isAuthenticated$", "subscribe", "isAuth", "currentUser$", "user", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "event", "preventDefault", "next", "navigate", "error", "console", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NavbarComponent_Template", "rf", "ctx", "NavbarComponent_ng_container_22_Template", "NavbarComponent_ng_container_23_Template", "ɵɵpureFunction0", "_c0", "i3", "NgIf", "RouterLink", "RouterLinkActive", "styles"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/components/shared/navbar/navbar.component.ts"], "sourcesContent": ["import { Component, OnInit, OnDestroy } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule, Router } from '@angular/router';\nimport { Subscription } from 'rxjs';\n\nimport { AuthService } from '../../../services/auth.service';\nimport { User } from '../../../models/auth.model';\n\n/**\n * Navigation bar component for the application.\n * \n * <AUTHOR>\n * @version 1.0.0\n */\n@Component({\n  selector: 'app-navbar',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <nav class=\"navbar navbar-expand-lg navbar-dark navbar-custom\">\n      <div class=\"container\">\n        <a class=\"navbar-brand navbar-brand-custom\" routerLink=\"/\">\n          <i class=\"fas fa-shopping-cart me-2\"></i>FreshMart\n        </a>\n        \n        <button class=\"navbar-toggler\" type=\"button\" data-bs-toggle=\"collapse\" \n                data-bs-target=\"#navbarNav\" aria-controls=\"navbarNav\" \n                aria-expanded=\"false\" aria-label=\"Toggle navigation\">\n          <span class=\"navbar-toggler-icon\"></span>\n        </button>\n        \n        <div class=\"collapse navbar-collapse\" id=\"navbarNav\">\n          <ul class=\"navbar-nav me-auto\">\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" routerLink=\"/\" routerLinkActive=\"active\" [routerLinkActiveOptions]=\"{exact: true}\">\n                <i class=\"fas fa-home me-1\"></i>Home\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" routerLink=\"/products\" routerLinkActive=\"active\">\n                <i class=\"fas fa-shopping-basket me-1\"></i>Products\n              </a>\n            </li>\n            <li class=\"nav-item\">\n              <a class=\"nav-link\" routerLink=\"/products/search\" routerLinkActive=\"active\">\n                <i class=\"fas fa-search me-1\"></i>Search\n              </a>\n            </li>\n          </ul>\n          \n          <ul class=\"navbar-nav\">\n            <!-- Guest Navigation -->\n            <ng-container *ngIf=\"!isAuthenticated\">\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" routerLink=\"/login\" routerLinkActive=\"active\">\n                  <i class=\"fas fa-sign-in-alt me-1\"></i>Login\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" routerLink=\"/register\" routerLinkActive=\"active\">\n                  <i class=\"fas fa-user-plus me-1\"></i>Register\n                </a>\n              </li>\n              <li class=\"nav-item\">\n                <a class=\"nav-link\" routerLink=\"/admin/login\" routerLinkActive=\"active\">\n                  <i class=\"fas fa-user-shield me-1\"></i>Admin\n                </a>\n              </li>\n            </ng-container>\n            \n            <!-- Authenticated User Navigation -->\n            <ng-container *ngIf=\"isAuthenticated && currentUser\">\n              <!-- Customer Navigation -->\n              <ng-container *ngIf=\"authService.isCustomer()\">\n                <li class=\"nav-item\">\n                  <a class=\"nav-link\" routerLink=\"/customer/dashboard\" routerLinkActive=\"active\">\n                    <i class=\"fas fa-tachometer-alt me-1\"></i>Dashboard\n                  </a>\n                </li>\n                <li class=\"nav-item\">\n                  <a class=\"nav-link\" routerLink=\"/customer/orders\" routerLinkActive=\"active\">\n                    <i class=\"fas fa-shopping-bag me-1\"></i>My Orders\n                  </a>\n                </li>\n                <li class=\"nav-item\">\n                  <a class=\"nav-link\" routerLink=\"/customer/profile\" routerLinkActive=\"active\">\n                    <i class=\"fas fa-user me-1\"></i>Profile\n                  </a>\n                </li>\n              </ng-container>\n              \n              <!-- Admin Navigation -->\n              <ng-container *ngIf=\"authService.isAdmin()\">\n                <li class=\"nav-item\">\n                  <a class=\"nav-link\" routerLink=\"/admin/dashboard\" routerLinkActive=\"active\">\n                    <i class=\"fas fa-tachometer-alt me-1\"></i>Dashboard\n                  </a>\n                </li>\n                <li class=\"nav-item dropdown\">\n                  <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"adminDropdown\" \n                     role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\n                    <i class=\"fas fa-cogs me-1\"></i>Manage\n                  </a>\n                  <ul class=\"dropdown-menu\" aria-labelledby=\"adminDropdown\">\n                    <li><a class=\"dropdown-item\" routerLink=\"/admin/customers\">\n                      <i class=\"fas fa-users me-2\"></i>Customers\n                    </a></li>\n                    <li><a class=\"dropdown-item\" routerLink=\"/admin/products\">\n                      <i class=\"fas fa-box me-2\"></i>Products\n                    </a></li>\n                    <li><a class=\"dropdown-item\" routerLink=\"/admin/orders\">\n                      <i class=\"fas fa-shopping-cart me-2\"></i>Orders\n                    </a></li>\n                  </ul>\n                </li>\n              </ng-container>\n              \n              <!-- User Menu -->\n              <li class=\"nav-item dropdown\">\n                <a class=\"nav-link dropdown-toggle\" href=\"#\" id=\"userDropdown\" \n                   role=\"button\" data-bs-toggle=\"dropdown\" aria-expanded=\"false\">\n                  <i class=\"fas fa-user-circle me-1\"></i>{{ currentUser.username }}\n                </a>\n                <ul class=\"dropdown-menu dropdown-menu-end\" aria-labelledby=\"userDropdown\">\n                  <li><h6 class=\"dropdown-header\">{{ currentUser.email }}</h6></li>\n                  <li><hr class=\"dropdown-divider\"></li>\n                  <li *ngIf=\"authService.isCustomer()\">\n                    <a class=\"dropdown-item\" routerLink=\"/customer/profile\">\n                      <i class=\"fas fa-user me-2\"></i>Profile\n                    </a>\n                  </li>\n                  <li>\n                    <a class=\"dropdown-item\" href=\"#\" (click)=\"logout($event)\">\n                      <i class=\"fas fa-sign-out-alt me-2\"></i>Logout\n                    </a>\n                  </li>\n                </ul>\n              </li>\n            </ng-container>\n          </ul>\n        </div>\n      </div>\n    </nav>\n  `,\n  styles: [`\n    .navbar-custom {\n      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);\n    }\n    \n    .navbar-brand-custom {\n      font-weight: bold;\n      font-size: 1.5rem;\n      color: white !important;\n    }\n    \n    .nav-link {\n      color: rgba(255, 255, 255, 0.9) !important;\n      transition: color 0.3s ease;\n    }\n    \n    .nav-link:hover,\n    .nav-link.active {\n      color: white !important;\n    }\n    \n    .dropdown-menu {\n      border: none;\n      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n    }\n    \n    .dropdown-item {\n      transition: background-color 0.3s ease;\n    }\n    \n    .dropdown-item:hover {\n      background-color: #f8f9fa;\n    }\n    \n    @media (max-width: 991px) {\n      .navbar-nav {\n        text-align: center;\n      }\n      \n      .dropdown-menu {\n        position: static !important;\n        float: none;\n        width: auto;\n        margin-top: 0;\n        background-color: transparent;\n        border: 0;\n        box-shadow: none;\n      }\n      \n      .dropdown-item {\n        color: rgba(255, 255, 255, 0.9) !important;\n      }\n      \n      .dropdown-item:hover {\n        background-color: rgba(255, 255, 255, 0.1);\n        color: white !important;\n      }\n    }\n  `]\n})\nexport class NavbarComponent implements OnInit, OnDestroy {\n  isAuthenticated = false;\n  currentUser: User | null = null;\n  private subscriptions: Subscription[] = [];\n\n  constructor(\n    public authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    // Subscribe to authentication status\n    this.subscriptions.push(\n      this.authService.isAuthenticated$.subscribe(\n        isAuth => this.isAuthenticated = isAuth\n      )\n    );\n\n    // Subscribe to current user\n    this.subscriptions.push(\n      this.authService.currentUser$.subscribe(\n        user => this.currentUser = user\n      )\n    );\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n\n  logout(event: Event): void {\n    event.preventDefault();\n    \n    this.authService.logout().subscribe({\n      next: () => {\n        this.router.navigate(['/']);\n      },\n      error: (error) => {\n        console.error('Logout error:', error);\n        // Even if logout fails on server, clear local session\n        this.router.navigate(['/']);\n      }\n    });\n  }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAgB,iBAAiB;;;;;;;;;;IAkD1CC,EAAA,CAAAC,uBAAA,GAAuC;IAEnCD,EADF,CAAAE,cAAA,YAAqB,YAC+C;IAChEF,EAAA,CAAAG,SAAA,YAAuC;IAAAH,EAAA,CAAAI,MAAA,aACzC;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;IAEHL,EADF,CAAAE,cAAA,YAAqB,YACkD;IACnEF,EAAA,CAAAG,SAAA,YAAqC;IAAAH,EAAA,CAAAI,MAAA,gBACvC;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;IAEHL,EADF,CAAAE,cAAA,YAAqB,aACqD;IACtEF,EAAA,CAAAG,SAAA,aAAuC;IAAAH,EAAA,CAAAI,MAAA,cACzC;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;;;;;;IAMLL,EAAA,CAAAC,uBAAA,GAA+C;IAE3CD,EADF,CAAAE,cAAA,YAAqB,YAC4D;IAC7EF,EAAA,CAAAG,SAAA,YAA0C;IAAAH,EAAA,CAAAI,MAAA,iBAC5C;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;IAEHL,EADF,CAAAE,cAAA,YAAqB,YACyD;IAC1EF,EAAA,CAAAG,SAAA,YAAwC;IAAAH,EAAA,CAAAI,MAAA,iBAC1C;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;IAEHL,EADF,CAAAE,cAAA,YAAqB,aAC0D;IAC3EF,EAAA,CAAAG,SAAA,aAAgC;IAAAH,EAAA,CAAAI,MAAA,gBAClC;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;;;;;;IAIPL,EAAA,CAAAC,uBAAA,GAA4C;IAExCD,EADF,CAAAE,cAAA,YAAqB,YACyD;IAC1EF,EAAA,CAAAG,SAAA,YAA0C;IAAAH,EAAA,CAAAI,MAAA,iBAC5C;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;IAEHL,EADF,CAAAE,cAAA,aAA8B,YAEqC;IAC/DF,EAAA,CAAAG,SAAA,YAAgC;IAAAH,EAAA,CAAAI,MAAA,cAClC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAEEL,EADN,CAAAE,cAAA,aAA0D,UACpD,aAAuD;IACzDF,EAAA,CAAAG,SAAA,aAAiC;IAAAH,EAAA,CAAAI,MAAA,kBACnC;IAAIJ,EAAJ,CAAAK,YAAA,EAAI,EAAK;IACLL,EAAJ,CAAAE,cAAA,UAAI,aAAsD;IACxDF,EAAA,CAAAG,SAAA,aAA+B;IAAAH,EAAA,CAAAI,MAAA,iBACjC;IAAIJ,EAAJ,CAAAK,YAAA,EAAI,EAAK;IACLL,EAAJ,CAAAE,cAAA,UAAI,aAAoD;IACtDF,EAAA,CAAAG,SAAA,YAAyC;IAAAH,EAAA,CAAAI,MAAA,eAC3C;IAEJJ,EAFI,CAAAK,YAAA,EAAI,EAAK,EACN,EACF;;;;;;IAaDL,EADF,CAAAE,cAAA,SAAqC,YACqB;IACtDF,EAAA,CAAAG,SAAA,YAAgC;IAAAH,EAAA,CAAAI,MAAA,eAClC;IACFJ,EADE,CAAAK,YAAA,EAAI,EACD;;;;;;IA3DXL,EAAA,CAAAC,uBAAA,GAAqD;IAqBnDD,EAnBA,CAAAM,UAAA,IAAAC,uDAAA,4BAA+C,IAAAC,uDAAA,4BAmBH;IA2B1CR,EADF,CAAAE,cAAA,aAA8B,YAEqC;IAC/DF,EAAA,CAAAG,SAAA,YAAuC;IAAAH,EAAA,CAAAI,MAAA,GACzC;IAAAJ,EAAA,CAAAK,YAAA,EAAI;IAEEL,EADN,CAAAE,cAAA,aAA2E,SACrE,aAA4B;IAAAF,EAAA,CAAAI,MAAA,IAAuB;IAAKJ,EAAL,CAAAK,YAAA,EAAK,EAAK;IACjEL,EAAA,CAAAE,cAAA,UAAI;IAAAF,EAAA,CAAAG,SAAA,cAA6B;IAAAH,EAAA,CAAAK,YAAA,EAAK;IACtCL,EAAA,CAAAM,UAAA,KAAAG,8CAAA,iBAAqC;IAMnCT,EADF,CAAAE,cAAA,UAAI,aACyD;IAAzBF,EAAA,CAAAU,UAAA,mBAAAC,6DAAAC,MAAA;MAAAZ,EAAA,CAAAa,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAf,EAAA,CAAAgB,aAAA;MAAA,OAAAhB,EAAA,CAAAiB,WAAA,CAASF,MAAA,CAAAG,MAAA,CAAAN,MAAA,CAAc;IAAA,EAAC;IACxDZ,EAAA,CAAAG,SAAA,aAAwC;IAAAH,EAAA,CAAAI,MAAA,eAC1C;IAGNJ,EAHM,CAAAK,YAAA,EAAI,EACD,EACF,EACF;;;;;IAhEUL,EAAA,CAAAmB,SAAA,EAA8B;IAA9BnB,EAAA,CAAAoB,UAAA,SAAAL,MAAA,CAAAM,WAAA,CAAAC,UAAA,GAA8B;IAmB9BtB,EAAA,CAAAmB,SAAA,EAA2B;IAA3BnB,EAAA,CAAAoB,UAAA,SAAAL,MAAA,CAAAM,WAAA,CAAAE,OAAA,GAA2B;IA6BCvB,EAAA,CAAAmB,SAAA,GACzC;IADyCnB,EAAA,CAAAwB,kBAAA,KAAAT,MAAA,CAAAU,WAAA,CAAAC,QAAA,MACzC;IAEkC1B,EAAA,CAAAmB,SAAA,GAAuB;IAAvBnB,EAAA,CAAA2B,iBAAA,CAAAZ,MAAA,CAAAU,WAAA,CAAAG,KAAA,CAAuB;IAElD5B,EAAA,CAAAmB,SAAA,GAA8B;IAA9BnB,EAAA,CAAAoB,UAAA,SAAAL,MAAA,CAAAM,WAAA,CAAAC,UAAA,GAA8B;;;AAtHrD;;;;;;AAqMA,OAAM,MAAOO,eAAe;EAK1BC,YACST,WAAwB,EACvBU,MAAc;IADf,KAAAV,WAAW,GAAXA,WAAW;IACV,KAAAU,MAAM,GAANA,MAAM;IANhB,KAAAC,eAAe,GAAG,KAAK;IACvB,KAAAP,WAAW,GAAgB,IAAI;IACvB,KAAAQ,aAAa,GAAmB,EAAE;EAKvC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,aAAa,CAACE,IAAI,CACrB,IAAI,CAACd,WAAW,CAACe,gBAAgB,CAACC,SAAS,CACzCC,MAAM,IAAI,IAAI,CAACN,eAAe,GAAGM,MAAM,CACxC,CACF;IAED;IACA,IAAI,CAACL,aAAa,CAACE,IAAI,CACrB,IAAI,CAACd,WAAW,CAACkB,YAAY,CAACF,SAAS,CACrCG,IAAI,IAAI,IAAI,CAACf,WAAW,GAAGe,IAAI,CAChC,CACF;EACH;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACR,aAAa,CAACS,OAAO,CAACC,GAAG,IAAIA,GAAG,CAACC,WAAW,EAAE,CAAC;EACtD;EAEA1B,MAAMA,CAAC2B,KAAY;IACjBA,KAAK,CAACC,cAAc,EAAE;IAEtB,IAAI,CAACzB,WAAW,CAACH,MAAM,EAAE,CAACmB,SAAS,CAAC;MAClCU,IAAI,EAAEA,CAAA,KAAK;QACT,IAAI,CAAChB,MAAM,CAACiB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7B,CAAC;MACDC,KAAK,EAAGA,KAAK,IAAI;QACfC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC;QACA,IAAI,CAAClB,MAAM,CAACiB,QAAQ,CAAC,CAAC,GAAG,CAAC,CAAC;MAC7B;KACD,CAAC;EACJ;;;uBA3CWnB,eAAe,EAAA7B,EAAA,CAAAmD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAArD,EAAA,CAAAmD,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAf1B,eAAe;MAAA2B,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAA1D,EAAA,CAAA2D,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxLpBjE,EAFJ,CAAAE,cAAA,aAA+D,aACtC,WACsC;UACzDF,EAAA,CAAAG,SAAA,WAAyC;UAAAH,EAAA,CAAAI,MAAA,iBAC3C;UAAAJ,EAAA,CAAAK,YAAA,EAAI;UAEJL,EAAA,CAAAE,cAAA,gBAE6D;UAC3DF,EAAA,CAAAG,SAAA,cAAyC;UAC3CH,EAAA,CAAAK,YAAA,EAAS;UAKHL,EAHN,CAAAE,cAAA,aAAqD,YACpB,YACR,YACoF;UACrGF,EAAA,CAAAG,SAAA,aAAgC;UAAAH,EAAA,CAAAI,MAAA,aAClC;UACFJ,EADE,CAAAK,YAAA,EAAI,EACD;UAEHL,EADF,CAAAE,cAAA,aAAqB,aACkD;UACnEF,EAAA,CAAAG,SAAA,aAA2C;UAAAH,EAAA,CAAAI,MAAA,iBAC7C;UACFJ,EADE,CAAAK,YAAA,EAAI,EACD;UAEHL,EADF,CAAAE,cAAA,aAAqB,aACyD;UAC1EF,EAAA,CAAAG,SAAA,aAAkC;UAAAH,EAAA,CAAAI,MAAA,eACpC;UAEJJ,EAFI,CAAAK,YAAA,EAAI,EACD,EACF;UAELL,EAAA,CAAAE,cAAA,cAAuB;UAqBrBF,EAnBA,CAAAM,UAAA,KAAA6D,wCAAA,4BAAuC,KAAAC,wCAAA,4BAmBc;UAuE7DpE,EAHM,CAAAK,YAAA,EAAK,EACD,EACF,EACF;;;UA5GiEL,EAAA,CAAAmB,SAAA,IAAyC;UAAzCnB,EAAA,CAAAoB,UAAA,4BAAApB,EAAA,CAAAqE,eAAA,IAAAC,GAAA,EAAyC;UAkBzFtE,EAAA,CAAAmB,SAAA,IAAsB;UAAtBnB,EAAA,CAAAoB,UAAA,UAAA8C,GAAA,CAAAlC,eAAA,CAAsB;UAmBtBhC,EAAA,CAAAmB,SAAA,EAAoC;UAApCnB,EAAA,CAAAoB,UAAA,SAAA8C,GAAA,CAAAlC,eAAA,IAAAkC,GAAA,CAAAzC,WAAA,CAAoC;;;qBAtDnD3B,YAAY,EAAAyE,EAAA,CAAAC,IAAA,EAAEzE,YAAY,EAAAuD,EAAA,CAAAmB,UAAA,EAAAnB,EAAA,CAAAoB,gBAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}