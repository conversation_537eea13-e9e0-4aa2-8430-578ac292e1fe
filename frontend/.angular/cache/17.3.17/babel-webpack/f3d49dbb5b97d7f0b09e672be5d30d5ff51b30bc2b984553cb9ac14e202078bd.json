{"ast": null, "code": "export const routes = [{\n  path: '',\n  loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent)\n}, {\n  path: 'login',\n  loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n}, {\n  path: 'register',\n  loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n}, {\n  path: 'admin/login',\n  loadComponent: () => import('./components/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent)\n}, {\n  path: 'customer',\n  loadChildren: () => import('./components/customer/customer.routes').then(m => m.customerRoutes)\n}, {\n  path: 'admin',\n  loadChildren: () => import('./components/admin/admin.routes').then(m => m.adminRoutes)\n}, {\n  path: 'products',\n  loadComponent: () => import('./components/products/product-list/product-list.component').then(m => m.ProductListComponent)\n}, {\n  path: 'products/search',\n  loadComponent: () => import('./components/products/product-search/product-search.component').then(m => m.ProductSearchComponent)\n}, {\n  path: '**',\n  redirectTo: ''\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "HomeComponent", "LoginComponent", "RegisterComponent", "AdminLoginComponent", "loadChildren", "customerRoutes", "adminRoutes", "ProductListComponent", "ProductSearchComponent", "redirectTo"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n\nexport const routes: Routes = [\n  {\n    path: '',\n    loadComponent: () => import('./components/home/<USER>').then(m => m.HomeComponent)\n  },\n  {\n    path: 'login',\n    loadComponent: () => import('./components/auth/login/login.component').then(m => m.LoginComponent)\n  },\n  {\n    path: 'register',\n    loadComponent: () => import('./components/auth/register/register.component').then(m => m.RegisterComponent)\n  },\n  {\n    path: 'admin/login',\n    loadComponent: () => import('./components/auth/admin-login/admin-login.component').then(m => m.AdminLoginComponent)\n  },\n  {\n    path: 'customer',\n    loadChildren: () => import('./components/customer/customer.routes').then(m => m.customerRoutes)\n  },\n  {\n    path: 'admin',\n    loadChildren: () => import('./components/admin/admin.routes').then(m => m.adminRoutes)\n  },\n  {\n    path: 'products',\n    loadComponent: () => import('./components/products/product-list/product-list.component').then(m => m.ProductListComponent)\n  },\n  {\n    path: 'products/search',\n    loadComponent: () => import('./components/products/product-search/product-search.component').then(m => m.ProductSearchComponent)\n  },\n  {\n    path: '**',\n    redirectTo: ''\n  }\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC5B;EACEC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,aAAa;CAC1F,EACD;EACEJ,IAAI,EAAE,OAAO;EACbC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,cAAc;CAClG,EACD;EACEL,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+CAA+C,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,iBAAiB;CAC3G,EACD;EACEN,IAAI,EAAE,aAAa;EACnBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,qDAAqD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,mBAAmB;CACnH,EACD;EACEP,IAAI,EAAE,UAAU;EAChBQ,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,cAAc;CAC/F,EACD;EACET,IAAI,EAAE,OAAO;EACbQ,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAACN,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,WAAW;CACtF,EACD;EACEV,IAAI,EAAE,UAAU;EAChBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,2DAA2D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,oBAAoB;CAC1H,EACD;EACEX,IAAI,EAAE,iBAAiB;EACvBC,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,+DAA+D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,sBAAsB;CAChI,EACD;EACEZ,IAAI,EAAE,IAAI;EACVa,UAAU,EAAE;CACb,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}