{"ast": null, "code": "import { throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\nimport * as i2 from \"./auth.service\";\n/**\n * Product service for handling product-related operations.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nexport class ProductService {\n  constructor(http, authService) {\n    this.http = http;\n    this.authService = authService;\n    this.API_URL = 'http://localhost:8080/api/products';\n    this.ADMIN_API_URL = 'http://localhost:8080/api/admin/products';\n  }\n  /**\n   * Get all products.\n   */\n  getAllProducts() {\n    return this.http.get(`${this.API_URL}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.products || response), catchError(this.handleError));\n  }\n  /**\n   * Get product by ID.\n   */\n  getProductById(id) {\n    return this.http.get(`${this.API_URL}/${id}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Search products by name.\n   */\n  searchProductsByName(productName) {\n    return this.http.get(`${this.API_URL}/search`, {\n      params: {\n        productName\n      },\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.products || response), catchError(this.handleError));\n  }\n  /**\n   * Search products by name or category.\n   */\n  searchProducts(searchTerm) {\n    return this.http.get(`${this.API_URL}/search/all`, {\n      params: {\n        searchTerm\n      },\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.products || response), catchError(this.handleError));\n  }\n  /**\n   * Get products by category.\n   */\n  getProductsByCategory(category) {\n    return this.http.get(`${this.API_URL}/category/${category}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.products || response), catchError(this.handleError));\n  }\n  /**\n   * Get products in stock.\n   */\n  getInStockProducts() {\n    return this.http.get(`${this.API_URL}/in-stock`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.products || response), catchError(this.handleError));\n  }\n  /**\n   * Get all categories.\n   */\n  getAllCategories() {\n    return this.http.get(`${this.API_URL}/categories`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.categories || response), catchError(this.handleError));\n  }\n  // Admin-only methods\n  /**\n   * Create new product (Admin only).\n   */\n  createProduct(product) {\n    return this.http.post(`${this.ADMIN_API_URL}`, product, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.product || response), catchError(this.handleError));\n  }\n  /**\n   * Update product (Admin only).\n   */\n  updateProduct(id, product) {\n    return this.http.put(`${this.ADMIN_API_URL}/${id}`, product, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(map(response => response.product || response), catchError(this.handleError));\n  }\n  /**\n   * Delete product (Admin only).\n   */\n  deleteProduct(id) {\n    return this.http.delete(`${this.ADMIN_API_URL}/${id}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Update product quantity (Admin only).\n   */\n  updateProductQuantity(id, quantity) {\n    return this.http.put(`${this.ADMIN_API_URL}/${id}/quantity`, {\n      quantity\n    }, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(catchError(this.handleError));\n  }\n  /**\n   * Handle HTTP errors.\n   */\n  handleError(error) {\n    let errorMessage = 'An error occurred';\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function ProductService_Factory(t) {\n      return new (t || ProductService)(i0.ɵɵinject(i1.HttpClient), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: ProductService,\n      factory: ProductService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["throwError", "map", "catchError", "ProductService", "constructor", "http", "authService", "API_URL", "ADMIN_API_URL", "getAllProducts", "get", "headers", "getAuthHeaders", "pipe", "response", "products", "handleError", "getProductById", "id", "searchProductsByName", "productName", "params", "searchProducts", "searchTerm", "getProductsByCategory", "category", "getInStockProducts", "getAllCategories", "categories", "createProduct", "product", "post", "updateProduct", "put", "deleteProduct", "delete", "updateProductQuantity", "quantity", "error", "errorMessage", "message", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/services/product.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { HttpClient } from '@angular/common/http';\nimport { Observable, throwError } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { Product, ProductCreate, ProductUpdate, ProductSearch } from '../models/product.model';\nimport { AuthService } from './auth.service';\n\n/**\n * Product service for handling product-related operations.\n * \n * <AUTHOR>\n * @version 1.0.0\n */\n@Injectable({\n  providedIn: 'root'\n})\nexport class ProductService {\n  private readonly API_URL = 'http://localhost:8080/api/products';\n  private readonly ADMIN_API_URL = 'http://localhost:8080/api/admin/products';\n\n  constructor(\n    private http: HttpClient,\n    private authService: AuthService\n  ) {}\n\n  /**\n   * Get all products.\n   */\n  getAllProducts(): Observable<Product[]> {\n    return this.http.get<any>(`${this.API_URL}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.products || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Get product by ID.\n   */\n  getProductById(id: number): Observable<Product> {\n    return this.http.get<Product>(`${this.API_URL}/${id}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Search products by name.\n   */\n  searchProductsByName(productName: string): Observable<Product[]> {\n    return this.http.get<any>(`${this.API_URL}/search`, {\n      params: { productName },\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.products || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Search products by name or category.\n   */\n  searchProducts(searchTerm: string): Observable<Product[]> {\n    return this.http.get<any>(`${this.API_URL}/search/all`, {\n      params: { searchTerm },\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.products || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Get products by category.\n   */\n  getProductsByCategory(category: string): Observable<Product[]> {\n    return this.http.get<any>(`${this.API_URL}/category/${category}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.products || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Get products in stock.\n   */\n  getInStockProducts(): Observable<Product[]> {\n    return this.http.get<any>(`${this.API_URL}/in-stock`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.products || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Get all categories.\n   */\n  getAllCategories(): Observable<string[]> {\n    return this.http.get<any>(`${this.API_URL}/categories`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.categories || response),\n      catchError(this.handleError)\n    );\n  }\n\n  // Admin-only methods\n\n  /**\n   * Create new product (Admin only).\n   */\n  createProduct(product: ProductCreate): Observable<Product> {\n    return this.http.post<any>(`${this.ADMIN_API_URL}`, product, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.product || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Update product (Admin only).\n   */\n  updateProduct(id: number, product: ProductUpdate): Observable<Product> {\n    return this.http.put<any>(`${this.ADMIN_API_URL}/${id}`, product, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      map(response => response.product || response),\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Delete product (Admin only).\n   */\n  deleteProduct(id: number): Observable<any> {\n    return this.http.delete<any>(`${this.ADMIN_API_URL}/${id}`, {\n      headers: this.authService.getAuthHeaders()\n    }).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Update product quantity (Admin only).\n   */\n  updateProductQuantity(id: number, quantity: number): Observable<any> {\n    return this.http.put<any>(`${this.ADMIN_API_URL}/${id}/quantity`, \n      { quantity }, \n      { headers: this.authService.getAuthHeaders() }\n    ).pipe(\n      catchError(this.handleError)\n    );\n  }\n\n  /**\n   * Handle HTTP errors.\n   */\n  private handleError(error: any): Observable<never> {\n    let errorMessage = 'An error occurred';\n    \n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n\n    return throwError(() => new Error(errorMessage));\n  }\n}\n"], "mappings": "AAEA,SAAqBA,UAAU,QAAQ,MAAM;AAC7C,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;;;;AAIhD;;;;;;AASA,OAAM,MAAOC,cAAc;EAIzBC,YACUC,IAAgB,EAChBC,WAAwB;IADxB,KAAAD,IAAI,GAAJA,IAAI;IACJ,KAAAC,WAAW,GAAXA,WAAW;IALJ,KAAAC,OAAO,GAAG,oCAAoC;IAC9C,KAAAC,aAAa,GAAG,0CAA0C;EAKxE;EAEH;;;EAGAC,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACJ,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,EAAE,EAAE;MAC3CI,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAAC,EAC9CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAC,cAAcA,CAACC,EAAU;IACvB,OAAO,IAAI,CAACb,IAAI,CAACK,GAAG,CAAU,GAAG,IAAI,CAACH,OAAO,IAAIW,EAAE,EAAE,EAAE;MACrDP,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAG,oBAAoBA,CAACC,WAAmB;IACtC,OAAO,IAAI,CAACf,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,SAAS,EAAE;MAClDc,MAAM,EAAE;QAAED;MAAW,CAAE;MACvBT,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAAC,EAC9CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAM,cAAcA,CAACC,UAAkB;IAC/B,OAAO,IAAI,CAAClB,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,aAAa,EAAE;MACtDc,MAAM,EAAE;QAAEE;MAAU,CAAE;MACtBZ,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAAC,EAC9CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAQ,qBAAqBA,CAACC,QAAgB;IACpC,OAAO,IAAI,CAACpB,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,aAAakB,QAAQ,EAAE,EAAE;MAChEd,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAAC,EAC9CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAU,kBAAkBA,CAAA;IAChB,OAAO,IAAI,CAACrB,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,WAAW,EAAE;MACpDI,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,IAAID,QAAQ,CAAC,EAC9CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAW,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACtB,IAAI,CAACK,GAAG,CAAM,GAAG,IAAI,CAACH,OAAO,aAAa,EAAE;MACtDI,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACc,UAAU,IAAId,QAAQ,CAAC,EAChDZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;EAEA;;;EAGAa,aAAaA,CAACC,OAAsB;IAClC,OAAO,IAAI,CAACzB,IAAI,CAAC0B,IAAI,CAAM,GAAG,IAAI,CAACvB,aAAa,EAAE,EAAEsB,OAAO,EAAE;MAC3DnB,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACgB,OAAO,IAAIhB,QAAQ,CAAC,EAC7CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAgB,aAAaA,CAACd,EAAU,EAAEY,OAAsB;IAC9C,OAAO,IAAI,CAACzB,IAAI,CAAC4B,GAAG,CAAM,GAAG,IAAI,CAACzB,aAAa,IAAIU,EAAE,EAAE,EAAEY,OAAO,EAAE;MAChEnB,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLZ,GAAG,CAACa,QAAQ,IAAIA,QAAQ,CAACgB,OAAO,IAAIhB,QAAQ,CAAC,EAC7CZ,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAkB,aAAaA,CAAChB,EAAU;IACtB,OAAO,IAAI,CAACb,IAAI,CAAC8B,MAAM,CAAM,GAAG,IAAI,CAAC3B,aAAa,IAAIU,EAAE,EAAE,EAAE;MAC1DP,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;KACzC,CAAC,CAACC,IAAI,CACLX,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGAoB,qBAAqBA,CAAClB,EAAU,EAAEmB,QAAgB;IAChD,OAAO,IAAI,CAAChC,IAAI,CAAC4B,GAAG,CAAM,GAAG,IAAI,CAACzB,aAAa,IAAIU,EAAE,WAAW,EAC9D;MAAEmB;IAAQ,CAAE,EACZ;MAAE1B,OAAO,EAAE,IAAI,CAACL,WAAW,CAACM,cAAc;IAAE,CAAE,CAC/C,CAACC,IAAI,CACJX,UAAU,CAAC,IAAI,CAACc,WAAW,CAAC,CAC7B;EACH;EAEA;;;EAGQA,WAAWA,CAACsB,KAAU;IAC5B,IAAIC,YAAY,GAAG,mBAAmB;IAEtC,IAAID,KAAK,CAACA,KAAK,EAAEE,OAAO,EAAE;MACxBD,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACE,OAAO;KACnC,MAAM,IAAIF,KAAK,CAACE,OAAO,EAAE;MACxBD,YAAY,GAAGD,KAAK,CAACE,OAAO;;IAG9B,OAAOxC,UAAU,CAAC,MAAM,IAAIyC,KAAK,CAACF,YAAY,CAAC,CAAC;EAClD;;;uBA5JWpC,cAAc,EAAAuC,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAAd5C,cAAc;MAAA6C,OAAA,EAAd7C,cAAc,CAAA8C,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}