{"ast": null, "code": "import { HttpHeaders } from \"@angular/common/http\";\nimport { BehaviorSubject, Observable, throwError } from \"rxjs\";\nimport { map, catchError } from \"rxjs/operators\";\nimport { UserRole } from \"../models/auth.model\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common/http\";\n/**\n * Authentication service for handling user login, logout, and token management.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nexport class AuthService {\n  constructor(http) {\n    this.http = http;\n    this.API_URL = \"http://localhost:8080/api/auth\";\n    this.TOKEN_KEY = \"token\";\n    this.USER_KEY = \"user\";\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    this.isAuthenticatedSubject = new BehaviorSubject(false);\n    this.isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n    this.loadUserFromStorage();\n  }\n  /**\n   * Login user with credentials.\n   */\n  login(credentials) {\n    return this.http.post(`${this.API_URL}/login`, credentials).pipe(map(response => {\n      if (response.success || response.token) {\n        this.setSession(response);\n      }\n      return response;\n    }), catchError(this.handleError));\n  }\n  /**\n   * Login admin user with credentials.\n   */\n  adminLogin(credentials) {\n    return this.http.post(`${this.API_URL}/admin/login`, credentials).pipe(map(response => {\n      if (response.success || response.token) {\n        this.setSession(response);\n      }\n      return response;\n    }), catchError(this.handleError));\n  }\n  /**\n   * Register new customer.\n   */\n  register(registration) {\n    return this.http.post(`${this.API_URL}/register`, registration).pipe(catchError(this.handleError));\n  }\n  /**\n   * Logout current user.\n   */\n  logout() {\n    return this.http.post(`${this.API_URL}/logout`, {}).pipe(map(() => {\n      this.clearSession();\n      return {\n        success: true,\n        message: \"Logged out successfully\"\n      };\n    }), catchError(() => {\n      this.clearSession();\n      return of({\n        success: true,\n        message: \"Logged out successfully\"\n      });\n    }));\n  }\n  /**\n   * Get current user.\n   */\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  /**\n   * Check if user is authenticated.\n   */\n  isAuthenticated() {\n    return this.isAuthenticatedSubject.value;\n  }\n  /**\n   * Check if user has admin role.\n   */\n  isAdmin() {\n    const user = this.getCurrentUser();\n    return user?.role === UserRole.ADMIN || user?.role === UserRole.SUPER_ADMIN;\n  }\n  /**\n   * Check if user has customer role.\n   */\n  isCustomer() {\n    const user = this.getCurrentUser();\n    return user?.role === UserRole.CUSTOMER;\n  }\n  /**\n   * Get authentication token.\n   */\n  getToken() {\n    return localStorage.getItem(this.TOKEN_KEY);\n  }\n  /**\n   * Get HTTP headers with authorization token.\n   */\n  getAuthHeaders() {\n    const token = this.getToken();\n    return new HttpHeaders({\n      \"Content-Type\": \"application/json\",\n      Authorization: token ? `Bearer ${token}` : \"\"\n    });\n  }\n  /**\n   * Validate token with server.\n   */\n  validateToken() {\n    const token = this.getToken();\n    if (!token) {\n      return new Observable(observer => {\n        observer.next(false);\n        observer.complete();\n      });\n    }\n    return this.http.post(`${this.API_URL}/validate`, {\n      token\n    }).pipe(map(response => response.valid), catchError(() => {\n      this.clearSession();\n      return of(false);\n    }));\n  }\n  /**\n   * Set authentication session.\n   */\n  setSession(authResponse) {\n    if (authResponse.token) {\n      localStorage.setItem(this.TOKEN_KEY, authResponse.token);\n    }\n    const user = {\n      id: authResponse.id,\n      username: authResponse.username,\n      email: authResponse.email,\n      role: authResponse.role,\n      isActive: true\n    };\n    localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n    this.currentUserSubject.next(user);\n    this.isAuthenticatedSubject.next(true);\n  }\n  /**\n   * Clear authentication session.\n   */\n  clearSession() {\n    localStorage.removeItem(this.TOKEN_KEY);\n    localStorage.removeItem(this.USER_KEY);\n    this.currentUserSubject.next(null);\n    this.isAuthenticatedSubject.next(false);\n  }\n  /**\n   * Load user from local storage.\n   */\n  loadUserFromStorage() {\n    const token = localStorage.getItem(this.TOKEN_KEY);\n    const userStr = localStorage.getItem(this.USER_KEY);\n    if (token && userStr) {\n      try {\n        const user = JSON.parse(userStr);\n        this.currentUserSubject.next(user);\n        this.isAuthenticatedSubject.next(true);\n      } catch (error) {\n        this.clearSession();\n      }\n    }\n  }\n  /**\n   * Handle HTTP errors.\n   */\n  handleError(error) {\n    let errorMessage = \"An error occurred\";\n    if (error.error?.message) {\n      errorMessage = error.error.message;\n    } else if (error.message) {\n      errorMessage = error.message;\n    }\n    return throwError(() => new Error(errorMessage));\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.HttpClient));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: \"root\"\n    });\n  }\n}", "map": {"version": 3, "names": ["HttpHeaders", "BehaviorSubject", "Observable", "throwError", "map", "catchError", "UserRole", "AuthService", "constructor", "http", "API_URL", "TOKEN_KEY", "USER_KEY", "currentUserSubject", "currentUser$", "asObservable", "isAuthenticatedSubject", "isAuthenticated$", "loadUserFromStorage", "login", "credentials", "post", "pipe", "response", "success", "token", "setSession", "handleError", "adminLogin", "register", "registration", "logout", "clearSession", "message", "of", "getCurrentUser", "value", "isAuthenticated", "isAdmin", "user", "role", "ADMIN", "SUPER_ADMIN", "isCustomer", "CUSTOMER", "getToken", "localStorage", "getItem", "getAuthHeaders", "Authorization", "validateToken", "observer", "next", "complete", "valid", "authResponse", "setItem", "id", "username", "email", "isActive", "JSON", "stringify", "removeItem", "userStr", "parse", "error", "errorMessage", "Error", "i0", "ɵɵinject", "i1", "HttpClient", "factory", "ɵfac", "providedIn"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/services/auth.service.ts"], "sourcesContent": ["import { Injectable } from \"@angular/core\";\nimport { HttpClient, HttpHeaders } from \"@angular/common/http\";\nimport { BehaviorSubject, Observable, throwError } from \"rxjs\";\nimport { map, catchError } from \"rxjs/operators\";\nimport {\n    LoginRequest,\n    AuthResponse,\n    User,\n    UserRole,\n} from \"../models/auth.model\";\nimport { CustomerRegistration } from \"../models/customer.model\";\n\n/**\n * Authentication service for handling user login, logout, and token management.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\n@Injectable({\n    providedIn: \"root\",\n})\nexport class AuthService {\n    private readonly API_URL = \"http://localhost:8080/api/auth\";\n    private readonly TOKEN_KEY = \"token\";\n    private readonly USER_KEY = \"user\";\n\n    private currentUserSubject = new BehaviorSubject<User | null>(null);\n    public currentUser$ = this.currentUserSubject.asObservable();\n\n    private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);\n    public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();\n\n    constructor(private http: HttpClient) {\n        this.loadUserFromStorage();\n    }\n\n    /**\n     * Login user with credentials.\n     */\n    login(credentials: LoginRequest): Observable<AuthResponse> {\n        return this.http\n            .post<AuthResponse>(`${this.API_URL}/login`, credentials)\n            .pipe(\n                map((response) => {\n                    if (response.success || response.token) {\n                        this.setSession(response);\n                    }\n                    return response;\n                }),\n                catchError(this.handleError)\n            );\n    }\n\n    /**\n     * Login admin user with credentials.\n     */\n    adminLogin(credentials: LoginRequest): Observable<AuthResponse> {\n        return this.http\n            .post<AuthResponse>(`${this.API_URL}/admin/login`, credentials)\n            .pipe(\n                map((response) => {\n                    if (response.success || response.token) {\n                        this.setSession(response);\n                    }\n                    return response;\n                }),\n                catchError(this.handleError)\n            );\n    }\n\n    /**\n     * Register new customer.\n     */\n    register(registration: CustomerRegistration): Observable<any> {\n        return this.http\n            .post(`${this.API_URL}/register`, registration)\n            .pipe(catchError(this.handleError));\n    }\n\n    /**\n     * Logout current user.\n     */\n    logout(): Observable<any> {\n        return this.http.post(`${this.API_URL}/logout`, {}).pipe(\n            map(() => {\n                this.clearSession();\n                return { success: true, message: \"Logged out successfully\" };\n            }),\n            catchError(() => {\n                this.clearSession();\n                return of({\n                    success: true,\n                    message: \"Logged out successfully\",\n                });\n            })\n        );\n    }\n\n    /**\n     * Get current user.\n     */\n    getCurrentUser(): User | null {\n        return this.currentUserSubject.value;\n    }\n\n    /**\n     * Check if user is authenticated.\n     */\n    isAuthenticated(): boolean {\n        return this.isAuthenticatedSubject.value;\n    }\n\n    /**\n     * Check if user has admin role.\n     */\n    isAdmin(): boolean {\n        const user = this.getCurrentUser();\n        return (\n            user?.role === UserRole.ADMIN || user?.role === UserRole.SUPER_ADMIN\n        );\n    }\n\n    /**\n     * Check if user has customer role.\n     */\n    isCustomer(): boolean {\n        const user = this.getCurrentUser();\n        return user?.role === UserRole.CUSTOMER;\n    }\n\n    /**\n     * Get authentication token.\n     */\n    getToken(): string | null {\n        return localStorage.getItem(this.TOKEN_KEY);\n    }\n\n    /**\n     * Get HTTP headers with authorization token.\n     */\n    getAuthHeaders(): HttpHeaders {\n        const token = this.getToken();\n        return new HttpHeaders({\n            \"Content-Type\": \"application/json\",\n            Authorization: token ? `Bearer ${token}` : \"\",\n        });\n    }\n\n    /**\n     * Validate token with server.\n     */\n    validateToken(): Observable<boolean> {\n        const token = this.getToken();\n        if (!token) {\n            return new Observable((observer) => {\n                observer.next(false);\n                observer.complete();\n            });\n        }\n\n        return this.http.post<any>(`${this.API_URL}/validate`, { token }).pipe(\n            map((response) => response.valid),\n            catchError(() => {\n                this.clearSession();\n                return of(false);\n            })\n        );\n    }\n\n    /**\n     * Set authentication session.\n     */\n    private setSession(authResponse: AuthResponse): void {\n        if (authResponse.token) {\n            localStorage.setItem(this.TOKEN_KEY, authResponse.token);\n        }\n\n        const user: User = {\n            id: authResponse.id!,\n            username: authResponse.username!,\n            email: authResponse.email!,\n            role: authResponse.role as UserRole,\n            isActive: true,\n        };\n\n        localStorage.setItem(this.USER_KEY, JSON.stringify(user));\n        this.currentUserSubject.next(user);\n        this.isAuthenticatedSubject.next(true);\n    }\n\n    /**\n     * Clear authentication session.\n     */\n    private clearSession(): void {\n        localStorage.removeItem(this.TOKEN_KEY);\n        localStorage.removeItem(this.USER_KEY);\n        this.currentUserSubject.next(null);\n        this.isAuthenticatedSubject.next(false);\n    }\n\n    /**\n     * Load user from local storage.\n     */\n    private loadUserFromStorage(): void {\n        const token = localStorage.getItem(this.TOKEN_KEY);\n        const userStr = localStorage.getItem(this.USER_KEY);\n\n        if (token && userStr) {\n            try {\n                const user = JSON.parse(userStr);\n                this.currentUserSubject.next(user);\n                this.isAuthenticatedSubject.next(true);\n            } catch (error) {\n                this.clearSession();\n            }\n        }\n    }\n\n    /**\n     * Handle HTTP errors.\n     */\n    private handleError(error: any): Observable<never> {\n        let errorMessage = \"An error occurred\";\n\n        if (error.error?.message) {\n            errorMessage = error.error.message;\n        } else if (error.message) {\n            errorMessage = error.message;\n        }\n\n        return throwError(() => new Error(errorMessage));\n    }\n}\n"], "mappings": "AACA,SAAqBA,WAAW,QAAQ,sBAAsB;AAC9D,SAASC,eAAe,EAAEC,UAAU,EAAEC,UAAU,QAAQ,MAAM;AAC9D,SAASC,GAAG,EAAEC,UAAU,QAAQ,gBAAgB;AAChD,SAIIC,QAAQ,QACL,sBAAsB;;;AAG7B;;;;;;AASA,OAAM,MAAOC,WAAW;EAWpBC,YAAoBC,IAAgB;IAAhB,KAAAA,IAAI,GAAJA,IAAI;IAVP,KAAAC,OAAO,GAAG,gCAAgC;IAC1C,KAAAC,SAAS,GAAG,OAAO;IACnB,KAAAC,QAAQ,GAAG,MAAM;IAE1B,KAAAC,kBAAkB,GAAG,IAAIZ,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAa,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAEpD,KAAAC,sBAAsB,GAAG,IAAIf,eAAe,CAAU,KAAK,CAAC;IAC7D,KAAAgB,gBAAgB,GAAG,IAAI,CAACD,sBAAsB,CAACD,YAAY,EAAE;IAGhE,IAAI,CAACG,mBAAmB,EAAE;EAC9B;EAEA;;;EAGAC,KAAKA,CAACC,WAAyB;IAC3B,OAAO,IAAI,CAACX,IAAI,CACXY,IAAI,CAAe,GAAG,IAAI,CAACX,OAAO,QAAQ,EAAEU,WAAW,CAAC,CACxDE,IAAI,CACDlB,GAAG,CAAEmB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,KAAK,EAAE;QACpC,IAAI,CAACC,UAAU,CAACH,QAAQ,CAAC;;MAE7B,OAAOA,QAAQ;IACnB,CAAC,CAAC,EACFlB,UAAU,CAAC,IAAI,CAACsB,WAAW,CAAC,CAC/B;EACT;EAEA;;;EAGAC,UAAUA,CAACR,WAAyB;IAChC,OAAO,IAAI,CAACX,IAAI,CACXY,IAAI,CAAe,GAAG,IAAI,CAACX,OAAO,cAAc,EAAEU,WAAW,CAAC,CAC9DE,IAAI,CACDlB,GAAG,CAAEmB,QAAQ,IAAI;MACb,IAAIA,QAAQ,CAACC,OAAO,IAAID,QAAQ,CAACE,KAAK,EAAE;QACpC,IAAI,CAACC,UAAU,CAACH,QAAQ,CAAC;;MAE7B,OAAOA,QAAQ;IACnB,CAAC,CAAC,EACFlB,UAAU,CAAC,IAAI,CAACsB,WAAW,CAAC,CAC/B;EACT;EAEA;;;EAGAE,QAAQA,CAACC,YAAkC;IACvC,OAAO,IAAI,CAACrB,IAAI,CACXY,IAAI,CAAC,GAAG,IAAI,CAACX,OAAO,WAAW,EAAEoB,YAAY,CAAC,CAC9CR,IAAI,CAACjB,UAAU,CAAC,IAAI,CAACsB,WAAW,CAAC,CAAC;EAC3C;EAEA;;;EAGAI,MAAMA,CAAA;IACF,OAAO,IAAI,CAACtB,IAAI,CAACY,IAAI,CAAC,GAAG,IAAI,CAACX,OAAO,SAAS,EAAE,EAAE,CAAC,CAACY,IAAI,CACpDlB,GAAG,CAAC,MAAK;MACL,IAAI,CAAC4B,YAAY,EAAE;MACnB,OAAO;QAAER,OAAO,EAAE,IAAI;QAAES,OAAO,EAAE;MAAyB,CAAE;IAChE,CAAC,CAAC,EACF5B,UAAU,CAAC,MAAK;MACZ,IAAI,CAAC2B,YAAY,EAAE;MACnB,OAAOE,EAAE,CAAC;QACNV,OAAO,EAAE,IAAI;QACbS,OAAO,EAAE;OACZ,CAAC;IACN,CAAC,CAAC,CACL;EACL;EAEA;;;EAGAE,cAAcA,CAAA;IACV,OAAO,IAAI,CAACtB,kBAAkB,CAACuB,KAAK;EACxC;EAEA;;;EAGAC,eAAeA,CAAA;IACX,OAAO,IAAI,CAACrB,sBAAsB,CAACoB,KAAK;EAC5C;EAEA;;;EAGAE,OAAOA,CAAA;IACH,MAAMC,IAAI,GAAG,IAAI,CAACJ,cAAc,EAAE;IAClC,OACII,IAAI,EAAEC,IAAI,KAAKlC,QAAQ,CAACmC,KAAK,IAAIF,IAAI,EAAEC,IAAI,KAAKlC,QAAQ,CAACoC,WAAW;EAE5E;EAEA;;;EAGAC,UAAUA,CAAA;IACN,MAAMJ,IAAI,GAAG,IAAI,CAACJ,cAAc,EAAE;IAClC,OAAOI,IAAI,EAAEC,IAAI,KAAKlC,QAAQ,CAACsC,QAAQ;EAC3C;EAEA;;;EAGAC,QAAQA,CAAA;IACJ,OAAOC,YAAY,CAACC,OAAO,CAAC,IAAI,CAACpC,SAAS,CAAC;EAC/C;EAEA;;;EAGAqC,cAAcA,CAAA;IACV,MAAMvB,KAAK,GAAG,IAAI,CAACoB,QAAQ,EAAE;IAC7B,OAAO,IAAI7C,WAAW,CAAC;MACnB,cAAc,EAAE,kBAAkB;MAClCiD,aAAa,EAAExB,KAAK,GAAG,UAAUA,KAAK,EAAE,GAAG;KAC9C,CAAC;EACN;EAEA;;;EAGAyB,aAAaA,CAAA;IACT,MAAMzB,KAAK,GAAG,IAAI,CAACoB,QAAQ,EAAE;IAC7B,IAAI,CAACpB,KAAK,EAAE;MACR,OAAO,IAAIvB,UAAU,CAAEiD,QAAQ,IAAI;QAC/BA,QAAQ,CAACC,IAAI,CAAC,KAAK,CAAC;QACpBD,QAAQ,CAACE,QAAQ,EAAE;MACvB,CAAC,CAAC;;IAGN,OAAO,IAAI,CAAC5C,IAAI,CAACY,IAAI,CAAM,GAAG,IAAI,CAACX,OAAO,WAAW,EAAE;MAAEe;IAAK,CAAE,CAAC,CAACH,IAAI,CAClElB,GAAG,CAAEmB,QAAQ,IAAKA,QAAQ,CAAC+B,KAAK,CAAC,EACjCjD,UAAU,CAAC,MAAK;MACZ,IAAI,CAAC2B,YAAY,EAAE;MACnB,OAAOE,EAAE,CAAC,KAAK,CAAC;IACpB,CAAC,CAAC,CACL;EACL;EAEA;;;EAGQR,UAAUA,CAAC6B,YAA0B;IACzC,IAAIA,YAAY,CAAC9B,KAAK,EAAE;MACpBqB,YAAY,CAACU,OAAO,CAAC,IAAI,CAAC7C,SAAS,EAAE4C,YAAY,CAAC9B,KAAK,CAAC;;IAG5D,MAAMc,IAAI,GAAS;MACfkB,EAAE,EAAEF,YAAY,CAACE,EAAG;MACpBC,QAAQ,EAAEH,YAAY,CAACG,QAAS;MAChCC,KAAK,EAAEJ,YAAY,CAACI,KAAM;MAC1BnB,IAAI,EAAEe,YAAY,CAACf,IAAgB;MACnCoB,QAAQ,EAAE;KACb;IAEDd,YAAY,CAACU,OAAO,CAAC,IAAI,CAAC5C,QAAQ,EAAEiD,IAAI,CAACC,SAAS,CAACvB,IAAI,CAAC,CAAC;IACzD,IAAI,CAAC1B,kBAAkB,CAACuC,IAAI,CAACb,IAAI,CAAC;IAClC,IAAI,CAACvB,sBAAsB,CAACoC,IAAI,CAAC,IAAI,CAAC;EAC1C;EAEA;;;EAGQpB,YAAYA,CAAA;IAChBc,YAAY,CAACiB,UAAU,CAAC,IAAI,CAACpD,SAAS,CAAC;IACvCmC,YAAY,CAACiB,UAAU,CAAC,IAAI,CAACnD,QAAQ,CAAC;IACtC,IAAI,CAACC,kBAAkB,CAACuC,IAAI,CAAC,IAAI,CAAC;IAClC,IAAI,CAACpC,sBAAsB,CAACoC,IAAI,CAAC,KAAK,CAAC;EAC3C;EAEA;;;EAGQlC,mBAAmBA,CAAA;IACvB,MAAMO,KAAK,GAAGqB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACpC,SAAS,CAAC;IAClD,MAAMqD,OAAO,GAAGlB,YAAY,CAACC,OAAO,CAAC,IAAI,CAACnC,QAAQ,CAAC;IAEnD,IAAIa,KAAK,IAAIuC,OAAO,EAAE;MAClB,IAAI;QACA,MAAMzB,IAAI,GAAGsB,IAAI,CAACI,KAAK,CAACD,OAAO,CAAC;QAChC,IAAI,CAACnD,kBAAkB,CAACuC,IAAI,CAACb,IAAI,CAAC;QAClC,IAAI,CAACvB,sBAAsB,CAACoC,IAAI,CAAC,IAAI,CAAC;OACzC,CAAC,OAAOc,KAAK,EAAE;QACZ,IAAI,CAAClC,YAAY,EAAE;;;EAG/B;EAEA;;;EAGQL,WAAWA,CAACuC,KAAU;IAC1B,IAAIC,YAAY,GAAG,mBAAmB;IAEtC,IAAID,KAAK,CAACA,KAAK,EAAEjC,OAAO,EAAE;MACtBkC,YAAY,GAAGD,KAAK,CAACA,KAAK,CAACjC,OAAO;KACrC,MAAM,IAAIiC,KAAK,CAACjC,OAAO,EAAE;MACtBkC,YAAY,GAAGD,KAAK,CAACjC,OAAO;;IAGhC,OAAO9B,UAAU,CAAC,MAAM,IAAIiE,KAAK,CAACD,YAAY,CAAC,CAAC;EACpD;;;uBAlNS5D,WAAW,EAAA8D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,UAAA;IAAA;EAAA;;;aAAXjE,WAAW;MAAAkE,OAAA,EAAXlE,WAAW,CAAAmE,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}