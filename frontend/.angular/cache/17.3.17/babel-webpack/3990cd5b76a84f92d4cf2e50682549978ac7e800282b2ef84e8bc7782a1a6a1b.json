{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterOutlet, NavigationEnd } from \"@angular/router\";\nimport { filter } from \"rxjs/operators\";\nimport { NavbarComponent } from \"./components/shared/navbar/navbar.component\";\nimport { FooterComponent } from \"./components/shared/footer/footer.component\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"./services/auth.service\";\n/**\n * Root component for the Online Grocery Ordering System.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nexport class AppComponent {\n  constructor(router, authService) {\n    this.router = router;\n    this.authService = authService;\n    this.title = \"Online Grocery Ordering System\";\n  }\n  ngOnInit() {\n    // Track route changes for analytics or other purposes\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd)).subscribe(event => {\n      // You can add analytics tracking here\n      if (event instanceof NavigationEnd) {\n        console.log(\"Navigation to:\", event.url);\n      }\n    });\n    // Validate token on app initialization\n    this.authService.validateToken().subscribe(isValid => {\n      if (!isValid && this.authService.getToken()) {\n        // Token is invalid, redirect to login\n        this.router.navigate([\"/login\"]);\n      }\n    });\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 5,\n      vars: 0,\n      consts: [[1, \"app-container\", \"d-flex\", \"flex-column\", \"min-vh-100\"], [1, \"flex-grow-1\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"app-navbar\");\n          i0.ɵɵelementStart(2, \"main\", 1);\n          i0.ɵɵelement(3, \"router-outlet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(4, \"app-footer\");\n          i0.ɵɵelementEnd();\n        }\n      },\n      dependencies: [CommonModule, RouterOutlet, NavbarComponent, FooterComponent],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  min-height: 100vh;\\n}\\n\\nmain[_ngcontent-%COMP%] {\\n  padding-top: 0;\\n}\\n\\n@media (max-width: 768px) {\\n  main[_ngcontent-%COMP%] {\\n    padding-top: 0;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvYXBwLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFDWTtFQUNJLGlCQUFBO0FBQWhCOztBQUdZO0VBQ0ksY0FBQTtBQUFoQjs7QUFHWTtFQUNJO0lBQ0ksY0FBQTtFQUFsQjtBQUNGIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgICAgICAgICAuYXBwLWNvbnRhaW5lciB7XG4gICAgICAgICAgICAgICAgbWluLWhlaWdodDogMTAwdmg7XG4gICAgICAgICAgICB9XG5cbiAgICAgICAgICAgIG1haW4ge1xuICAgICAgICAgICAgICAgIHBhZGRpbmctdG9wOiAwO1xuICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICBAbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgICAgICAgICAgICAgICBtYWluIHtcbiAgICAgICAgICAgICAgICAgICAgcGFkZGluZy10b3A6IDA7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterOutlet", "NavigationEnd", "filter", "NavbarComponent", "FooterComponent", "AppComponent", "constructor", "router", "authService", "title", "ngOnInit", "events", "pipe", "event", "subscribe", "console", "log", "url", "validateToken", "<PERSON><PERSON><PERSON><PERSON>", "getToken", "navigate", "i0", "ɵɵdirectiveInject", "i1", "Router", "i2", "AuthService", "selectors", "standalone", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "styles"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/app.component.ts"], "sourcesContent": ["import { Component, OnInit } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterOutlet, Router, NavigationEnd } from \"@angular/router\";\nimport { filter } from \"rxjs/operators\";\n\nimport { NavbarComponent } from \"./components/shared/navbar/navbar.component\";\nimport { FooterComponent } from \"./components/shared/footer/footer.component\";\nimport { AuthService } from \"./services/auth.service\";\n\n/**\n * Root component for the Online Grocery Ordering System.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\n@Component({\n    selector: \"app-root\",\n    standalone: true,\n    imports: [CommonModule, RouterOutlet, NavbarComponent, FooterComponent],\n    template: `\n        <div class=\"app-container d-flex flex-column min-vh-100\">\n            <app-navbar></app-navbar>\n\n            <main class=\"flex-grow-1\">\n                <router-outlet></router-outlet>\n            </main>\n\n            <app-footer></app-footer>\n        </div>\n    `,\n    styles: [\n        `\n            .app-container {\n                min-height: 100vh;\n            }\n\n            main {\n                padding-top: 0;\n            }\n\n            @media (max-width: 768px) {\n                main {\n                    padding-top: 0;\n                }\n            }\n        `,\n    ],\n})\nexport class AppComponent implements OnInit {\n    title = \"Online Grocery Ordering System\";\n\n    constructor(private router: Router, private authService: AuthService) {}\n\n    ngOnInit(): void {\n        // Track route changes for analytics or other purposes\n        this.router.events\n            .pipe(filter((event) => event instanceof NavigationEnd))\n            .subscribe((event) => {\n                // You can add analytics tracking here\n                if (event instanceof NavigationEnd) {\n                    console.log(\"Navigation to:\", event.url);\n                }\n            });\n\n        // Validate token on app initialization\n        this.authService.validateToken().subscribe((isValid) => {\n            if (!isValid && this.authService.getToken()) {\n                // Token is invalid, redirect to login\n                this.router.navigate([\"/login\"]);\n            }\n        });\n    }\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,EAAUC,aAAa,QAAQ,iBAAiB;AACrE,SAASC,MAAM,QAAQ,gBAAgB;AAEvC,SAASC,eAAe,QAAQ,6CAA6C;AAC7E,SAASC,eAAe,QAAQ,6CAA6C;;;;AAG7E;;;;;;AAuCA,OAAM,MAAOC,YAAY;EAGrBC,YAAoBC,MAAc,EAAUC,WAAwB;IAAhD,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,WAAW,GAAXA,WAAW;IAFvD,KAAAC,KAAK,GAAG,gCAAgC;EAE+B;EAEvEC,QAAQA,CAAA;IACJ;IACA,IAAI,CAACH,MAAM,CAACI,MAAM,CACbC,IAAI,CAACV,MAAM,CAAEW,KAAK,IAAKA,KAAK,YAAYZ,aAAa,CAAC,CAAC,CACvDa,SAAS,CAAED,KAAK,IAAI;MACjB;MACA,IAAIA,KAAK,YAAYZ,aAAa,EAAE;QAChCc,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEH,KAAK,CAACI,GAAG,CAAC;;IAEhD,CAAC,CAAC;IAEN;IACA,IAAI,CAACT,WAAW,CAACU,aAAa,EAAE,CAACJ,SAAS,CAAEK,OAAO,IAAI;MACnD,IAAI,CAACA,OAAO,IAAI,IAAI,CAACX,WAAW,CAACY,QAAQ,EAAE,EAAE;QACzC;QACA,IAAI,CAACb,MAAM,CAACc,QAAQ,CAAC,CAAC,QAAQ,CAAC,CAAC;;IAExC,CAAC,CAAC;EACN;;;uBAvBShB,YAAY,EAAAiB,EAAA,CAAAC,iBAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,iBAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAZtB,YAAY;MAAAuB,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAR,EAAA,CAAAS,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5BjBf,EAAA,CAAAiB,cAAA,aAAyD;UACrDjB,EAAA,CAAAkB,SAAA,iBAAyB;UAEzBlB,EAAA,CAAAiB,cAAA,cAA0B;UACtBjB,EAAA,CAAAkB,SAAA,oBAA+B;UACnClB,EAAA,CAAAmB,YAAA,EAAO;UAEPnB,EAAA,CAAAkB,SAAA,iBAAyB;UAC7BlB,EAAA,CAAAmB,YAAA,EAAM;;;qBAVA1C,YAAY,EAAEC,YAAY,EAAEG,eAAe,EAAEC,eAAe;MAAAsC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}