{"ast": null, "code": "import { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\n/**\n * Footer component for the application.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nexport class FooterComponent {\n  constructor() {\n    this.currentYear = new Date().getFullYear();\n  }\n  static {\n    this.ɵfac = function FooterComponent_Factory(t) {\n      return new (t || FooterComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: FooterComponent,\n      selectors: [[\"app-footer\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 95,\n      vars: 1,\n      consts: [[1, \"footer-custom\", \"mt-auto\"], [1, \"container\"], [1, \"row\"], [1, \"col-md-6\", \"col-lg-4\", \"mb-4\"], [1, \"text-white\", \"mb-3\"], [1, \"fas\", \"fa-shopping-cart\", \"me-2\"], [1, \"text-light\"], [1, \"social-links\"], [\"href\", \"#\", \"aria-label\", \"Facebook\", 1, \"text-light\", \"me-3\"], [1, \"fab\", \"fa-facebook-f\"], [\"href\", \"#\", \"aria-label\", \"Twitter\", 1, \"text-light\", \"me-3\"], [1, \"fab\", \"fa-twitter\"], [\"href\", \"#\", \"aria-label\", \"Instagram\", 1, \"text-light\", \"me-3\"], [1, \"fab\", \"fa-instagram\"], [\"href\", \"#\", \"aria-label\", \"LinkedIn\", 1, \"text-light\"], [1, \"fab\", \"fa-linkedin-in\"], [1, \"col-md-6\", \"col-lg-2\", \"mb-4\"], [1, \"list-unstyled\"], [1, \"mb-2\"], [\"routerLink\", \"/\", 1, \"text-light\", \"text-decoration-none\"], [1, \"fas\", \"fa-home\", \"me-2\"], [\"routerLink\", \"/products\", 1, \"text-light\", \"text-decoration-none\"], [1, \"fas\", \"fa-shopping-basket\", \"me-2\"], [\"routerLink\", \"/products/search\", 1, \"text-light\", \"text-decoration-none\"], [1, \"fas\", \"fa-search\", \"me-2\"], [\"routerLink\", \"/register\", 1, \"text-light\", \"text-decoration-none\"], [1, \"fas\", \"fa-user-plus\", \"me-2\"], [1, \"col-md-6\", \"col-lg-3\", \"mb-4\"], [\"href\", \"#\", 1, \"text-light\", \"text-decoration-none\"], [1, \"fas\", \"fa-apple-alt\", \"me-2\"], [1, \"fas\", \"fa-carrot\", \"me-2\"], [1, \"fas\", \"fa-cheese\", \"me-2\"], [1, \"fas\", \"fa-bread-slice\", \"me-2\"], [1, \"mb-2\", \"text-light\"], [1, \"fas\", \"fa-map-marker-alt\", \"me-2\"], [1, \"fas\", \"fa-phone\", \"me-2\"], [1, \"fas\", \"fa-envelope\", \"me-2\"], [1, \"fas\", \"fa-clock\", \"me-2\"], [1, \"my-4\", \"bg-light\"], [1, \"row\", \"align-items-center\"], [1, \"col-md-6\"], [1, \"text-light\", \"mb-0\"], [1, \"col-md-6\", \"text-md-end\"], [1, \"fas\", \"fa-heart\", \"text-danger\"], [1, \"row\", \"mt-3\"], [1, \"col-12\", \"text-center\"], [\"href\", \"#\", 1, \"text-light\", \"text-decoration-none\", \"me-3\"]],\n      template: function FooterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"footer\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"div\", 3)(4, \"h5\", 4);\n          i0.ɵɵelement(5, \"i\", 5);\n          i0.ɵɵtext(6, \"FreshMart \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\", 6);\n          i0.ɵɵtext(8, \" Your trusted partner for fresh groceries and quality products. We deliver farm-fresh produce right to your doorstep. \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"div\", 7)(10, \"a\", 8);\n          i0.ɵɵelement(11, \"i\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(12, \"a\", 10);\n          i0.ɵɵelement(13, \"i\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(14, \"a\", 12);\n          i0.ɵɵelement(15, \"i\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(16, \"a\", 14);\n          i0.ɵɵelement(17, \"i\", 15);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(18, \"div\", 16)(19, \"h6\", 4);\n          i0.ɵɵtext(20, \"Quick Links\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"ul\", 17)(22, \"li\", 18)(23, \"a\", 19);\n          i0.ɵɵelement(24, \"i\", 20);\n          i0.ɵɵtext(25, \"Home \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"li\", 18)(27, \"a\", 21);\n          i0.ɵɵelement(28, \"i\", 22);\n          i0.ɵɵtext(29, \"Products \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"li\", 18)(31, \"a\", 23);\n          i0.ɵɵelement(32, \"i\", 24);\n          i0.ɵɵtext(33, \"Search \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(34, \"li\", 18)(35, \"a\", 25);\n          i0.ɵɵelement(36, \"i\", 26);\n          i0.ɵɵtext(37, \"Register \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(38, \"div\", 27)(39, \"h6\", 4);\n          i0.ɵɵtext(40, \"Categories\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"ul\", 17)(42, \"li\", 18)(43, \"a\", 28);\n          i0.ɵɵelement(44, \"i\", 29);\n          i0.ɵɵtext(45, \"Fruits \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(46, \"li\", 18)(47, \"a\", 28);\n          i0.ɵɵelement(48, \"i\", 30);\n          i0.ɵɵtext(49, \"Vegetables \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(50, \"li\", 18)(51, \"a\", 28);\n          i0.ɵɵelement(52, \"i\", 31);\n          i0.ɵɵtext(53, \"Dairy \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(54, \"li\", 18)(55, \"a\", 28);\n          i0.ɵɵelement(56, \"i\", 32);\n          i0.ɵɵtext(57, \"Bakery \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(58, \"div\", 27)(59, \"h6\", 4);\n          i0.ɵɵtext(60, \"Contact Info\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(61, \"ul\", 17)(62, \"li\", 33);\n          i0.ɵɵelement(63, \"i\", 34);\n          i0.ɵɵtext(64, \" 123 Grocery Street, Food City, FC 12345 \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(65, \"li\", 33);\n          i0.ɵɵelement(66, \"i\", 35);\n          i0.ɵɵtext(67, \" +**************** \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(68, \"li\", 33);\n          i0.ɵɵelement(69, \"i\", 36);\n          i0.ɵɵtext(70, \" <EMAIL> \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(71, \"li\", 33);\n          i0.ɵɵelement(72, \"i\", 37);\n          i0.ɵɵtext(73, \" 24/7 Customer Support \");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelement(74, \"hr\", 38);\n          i0.ɵɵelementStart(75, \"div\", 39)(76, \"div\", 40)(77, \"p\", 41);\n          i0.ɵɵtext(78);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(79, \"div\", 42)(80, \"p\", 41);\n          i0.ɵɵtext(81, \" Developed with \");\n          i0.ɵɵelement(82, \"i\", 43);\n          i0.ɵɵtext(83, \" by \");\n          i0.ɵɵelementStart(84, \"strong\");\n          i0.ɵɵtext(85, \"Chirag Singhal\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(86, \"div\", 44)(87, \"div\", 45)(88, \"small\", 6)(89, \"a\", 46);\n          i0.ɵɵtext(90, \"Privacy Policy\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(91, \"a\", 46);\n          i0.ɵɵtext(92, \"Terms of Service\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(93, \"a\", 28);\n          i0.ɵɵtext(94, \"Cookie Policy\");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(78);\n          i0.ɵɵtextInterpolate1(\" \\u00A9 \", ctx.currentYear, \" Online Grocery Ordering System. All rights reserved. \");\n        }\n      },\n      dependencies: [CommonModule, RouterModule, i1.RouterLink],\n      styles: [\".footer-custom[_ngcontent-%COMP%] {\\n  background: #343a40;\\n  color: white;\\n  padding: 3rem 0 1rem;\\n}\\n\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 40px;\\n  height: 40px;\\n  line-height: 40px;\\n  text-align: center;\\n  border-radius: 50%;\\n  background: rgba(255, 255, 255, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.social-links[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: translateY(-2px);\\n}\\n\\n.list-unstyled[_ngcontent-%COMP%]   a[_ngcontent-%COMP%] {\\n  transition: color 0.3s ease;\\n}\\n\\n.list-unstyled[_ngcontent-%COMP%]   a[_ngcontent-%COMP%]:hover {\\n  color: #667eea !important;\\n}\\n\\nhr[_ngcontent-%COMP%] {\\n  opacity: 0.3;\\n}\\n\\n@media (max-width: 768px) {\\n  .footer-custom[_ngcontent-%COMP%] {\\n    padding: 2rem 0 1rem;\\n  }\\n  .col-md-6.text-md-end[_ngcontent-%COMP%] {\\n    text-align: center !important;\\n    margin-top: 1rem;\\n  }\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "FooterComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "i1", "RouterLink", "styles"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/components/shared/footer/footer.component.ts"], "sourcesContent": ["import { Component } from \"@angular/core\";\nimport { CommonModule } from \"@angular/common\";\nimport { RouterModule } from \"@angular/router\";\n\n/**\n * Footer component for the application.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\n@Component({\n    selector: \"app-footer\",\n    standalone: true,\n    imports: [CommonModule, RouterModule],\n    template: `\n        <footer class=\"footer-custom mt-auto\">\n            <div class=\"container\">\n                <div class=\"row\">\n                    <div class=\"col-md-6 col-lg-4 mb-4\">\n                        <h5 class=\"text-white mb-3\">\n                            <i class=\"fas fa-shopping-cart me-2\"></i>FreshMart\n                        </h5>\n                        <p class=\"text-light\">\n                            Your trusted partner for fresh groceries and quality\n                            products. We deliver farm-fresh produce right to\n                            your doorstep.\n                        </p>\n                        <div class=\"social-links\">\n                            <a\n                                href=\"#\"\n                                class=\"text-light me-3\"\n                                aria-label=\"Facebook\"\n                            >\n                                <i class=\"fab fa-facebook-f\"></i>\n                            </a>\n                            <a\n                                href=\"#\"\n                                class=\"text-light me-3\"\n                                aria-label=\"Twitter\"\n                            >\n                                <i class=\"fab fa-twitter\"></i>\n                            </a>\n                            <a\n                                href=\"#\"\n                                class=\"text-light me-3\"\n                                aria-label=\"Instagram\"\n                            >\n                                <i class=\"fab fa-instagram\"></i>\n                            </a>\n                            <a\n                                href=\"#\"\n                                class=\"text-light\"\n                                aria-label=\"LinkedIn\"\n                            >\n                                <i class=\"fab fa-linkedin-in\"></i>\n                            </a>\n                        </div>\n                    </div>\n\n                    <div class=\"col-md-6 col-lg-2 mb-4\">\n                        <h6 class=\"text-white mb-3\">Quick Links</h6>\n                        <ul class=\"list-unstyled\">\n                            <li class=\"mb-2\">\n                                <a\n                                    routerLink=\"/\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-home me-2\"></i>Home\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    routerLink=\"/products\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-shopping-basket me-2\"></i\n                                    >Products\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    routerLink=\"/products/search\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-search me-2\"></i>Search\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    routerLink=\"/register\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-user-plus me-2\"></i\n                                    >Register\n                                </a>\n                            </li>\n                        </ul>\n                    </div>\n\n                    <div class=\"col-md-6 col-lg-3 mb-4\">\n                        <h6 class=\"text-white mb-3\">Categories</h6>\n                        <ul class=\"list-unstyled\">\n                            <li class=\"mb-2\">\n                                <a\n                                    href=\"#\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-apple-alt me-2\"></i>Fruits\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    href=\"#\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-carrot me-2\"></i>Vegetables\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    href=\"#\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-cheese me-2\"></i>Dairy\n                                </a>\n                            </li>\n                            <li class=\"mb-2\">\n                                <a\n                                    href=\"#\"\n                                    class=\"text-light text-decoration-none\"\n                                >\n                                    <i class=\"fas fa-bread-slice me-2\"></i\n                                    >Bakery\n                                </a>\n                            </li>\n                        </ul>\n                    </div>\n\n                    <div class=\"col-md-6 col-lg-3 mb-4\">\n                        <h6 class=\"text-white mb-3\">Contact Info</h6>\n                        <ul class=\"list-unstyled\">\n                            <li class=\"mb-2 text-light\">\n                                <i class=\"fas fa-map-marker-alt me-2\"></i>\n                                123 Grocery Street, Food City, FC 12345\n                            </li>\n                            <li class=\"mb-2 text-light\">\n                                <i class=\"fas fa-phone me-2\"></i>\n                                +****************\n                            </li>\n                            <li class=\"mb-2 text-light\">\n                                <i class=\"fas fa-envelope me-2\"></i>\n                                info&#64;freshmart.com\n                            </li>\n                            <li class=\"mb-2 text-light\">\n                                <i class=\"fas fa-clock me-2\"></i>\n                                24/7 Customer Support\n                            </li>\n                        </ul>\n                    </div>\n                </div>\n\n                <hr class=\"my-4 bg-light\" />\n\n                <div class=\"row align-items-center\">\n                    <div class=\"col-md-6\">\n                        <p class=\"text-light mb-0\">\n                            &copy; {{ currentYear }} Online Grocery Ordering\n                            System. All rights reserved.\n                        </p>\n                    </div>\n                    <div class=\"col-md-6 text-md-end\">\n                        <p class=\"text-light mb-0\">\n                            Developed with\n                            <i class=\"fas fa-heart text-danger\"></i> by\n                            <strong>Chirag Singhal</strong>\n                        </p>\n                    </div>\n                </div>\n\n                <div class=\"row mt-3\">\n                    <div class=\"col-12 text-center\">\n                        <small class=\"text-light\">\n                            <a\n                                href=\"#\"\n                                class=\"text-light text-decoration-none me-3\"\n                                >Privacy Policy</a\n                            >\n                            <a\n                                href=\"#\"\n                                class=\"text-light text-decoration-none me-3\"\n                                >Terms of Service</a\n                            >\n                            <a href=\"#\" class=\"text-light text-decoration-none\"\n                                >Cookie Policy</a\n                            >\n                        </small>\n                    </div>\n                </div>\n            </div>\n        </footer>\n    `,\n    styles: [\n        `\n            .footer-custom {\n                background: #343a40;\n                color: white;\n                padding: 3rem 0 1rem;\n            }\n\n            .social-links a {\n                display: inline-block;\n                width: 40px;\n                height: 40px;\n                line-height: 40px;\n                text-align: center;\n                border-radius: 50%;\n                background: rgba(255, 255, 255, 0.1);\n                transition: all 0.3s ease;\n            }\n\n            .social-links a:hover {\n                background: rgba(255, 255, 255, 0.2);\n                transform: translateY(-2px);\n            }\n\n            .list-unstyled a {\n                transition: color 0.3s ease;\n            }\n\n            .list-unstyled a:hover {\n                color: #667eea !important;\n            }\n\n            hr {\n                opacity: 0.3;\n            }\n\n            @media (max-width: 768px) {\n                .footer-custom {\n                    padding: 2rem 0 1rem;\n                }\n\n                .col-md-6.text-md-end {\n                    text-align: center !important;\n                    margin-top: 1rem;\n                }\n            }\n        `,\n    ],\n})\nexport class FooterComponent {\n    currentYear = new Date().getFullYear();\n}\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;;;AAE9C;;;;;;AAsPA,OAAM,MAAOC,eAAe;EAhP5BC,YAAA;IAiPI,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;;;;uBAD7BJ,eAAe;IAAA;EAAA;;;YAAfA,eAAe;MAAAK,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAvOJP,EAJhB,CAAAS,cAAA,gBAAsC,aACX,aACF,aACuB,YACJ;UACxBT,EAAA,CAAAU,SAAA,WAAyC;UAAAV,EAAA,CAAAW,MAAA,iBAC7C;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAS,cAAA,WAAsB;UAClBT,EAAA,CAAAW,MAAA,6HAGJ;UAAAX,EAAA,CAAAY,YAAA,EAAI;UAEAZ,EADJ,CAAAS,cAAA,aAA0B,YAKrB;UACGT,EAAA,CAAAU,SAAA,YAAiC;UACrCV,EAAA,CAAAY,YAAA,EAAI;UACJZ,EAAA,CAAAS,cAAA,aAIC;UACGT,EAAA,CAAAU,SAAA,aAA8B;UAClCV,EAAA,CAAAY,YAAA,EAAI;UACJZ,EAAA,CAAAS,cAAA,aAIC;UACGT,EAAA,CAAAU,SAAA,aAAgC;UACpCV,EAAA,CAAAY,YAAA,EAAI;UACJZ,EAAA,CAAAS,cAAA,aAIC;UACGT,EAAA,CAAAU,SAAA,aAAkC;UAG9CV,EAFQ,CAAAY,YAAA,EAAI,EACF,EACJ;UAGFZ,EADJ,CAAAS,cAAA,eAAoC,aACJ;UAAAT,EAAA,CAAAW,MAAA,mBAAW;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAGpCZ,EAFR,CAAAS,cAAA,cAA0B,cACL,aAIZ;UACGT,EAAA,CAAAU,SAAA,aAAgC;UAAAV,EAAA,CAAAW,MAAA,aACpC;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aACC;UAAAV,EAAA,CAAAW,MAAA,iBACL;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aAAkC;UAAAV,EAAA,CAAAW,MAAA,eACtC;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aACC;UAAAV,EAAA,CAAAW,MAAA,iBACL;UAGZX,EAHY,CAAAY,YAAA,EAAI,EACH,EACJ,EACH;UAGFZ,EADJ,CAAAS,cAAA,eAAoC,aACJ;UAAAT,EAAA,CAAAW,MAAA,kBAAU;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAGnCZ,EAFR,CAAAS,cAAA,cAA0B,cACL,aAIZ;UACGT,EAAA,CAAAU,SAAA,aAAqC;UAAAV,EAAA,CAAAW,MAAA,eACzC;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aAAkC;UAAAV,EAAA,CAAAW,MAAA,mBACtC;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aAAkC;UAAAV,EAAA,CAAAW,MAAA,cACtC;UACJX,EADI,CAAAY,YAAA,EAAI,EACH;UAEDZ,EADJ,CAAAS,cAAA,cAAiB,aAIZ;UACGT,EAAA,CAAAU,SAAA,aACC;UAAAV,EAAA,CAAAW,MAAA,eACL;UAGZX,EAHY,CAAAY,YAAA,EAAI,EACH,EACJ,EACH;UAGFZ,EADJ,CAAAS,cAAA,eAAoC,aACJ;UAAAT,EAAA,CAAAW,MAAA,oBAAY;UAAAX,EAAA,CAAAY,YAAA,EAAK;UAEzCZ,EADJ,CAAAS,cAAA,cAA0B,cACM;UACxBT,EAAA,CAAAU,SAAA,aAA0C;UAC1CV,EAAA,CAAAW,MAAA,iDACJ;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAS,cAAA,cAA4B;UACxBT,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAW,MAAA,2BACJ;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAS,cAAA,cAA4B;UACxBT,EAAA,CAAAU,SAAA,aAAoC;UACpCV,EAAA,CAAAW,MAAA,4BACJ;UAAAX,EAAA,CAAAY,YAAA,EAAK;UACLZ,EAAA,CAAAS,cAAA,cAA4B;UACxBT,EAAA,CAAAU,SAAA,aAAiC;UACjCV,EAAA,CAAAW,MAAA,+BACJ;UAGZX,EAHY,CAAAY,YAAA,EAAK,EACJ,EACH,EACJ;UAENZ,EAAA,CAAAU,SAAA,cAA4B;UAIpBV,EAFR,CAAAS,cAAA,eAAoC,eACV,aACS;UACvBT,EAAA,CAAAW,MAAA,IAEJ;UACJX,EADI,CAAAY,YAAA,EAAI,EACF;UAEFZ,EADJ,CAAAS,cAAA,eAAkC,aACH;UACvBT,EAAA,CAAAW,MAAA,wBACA;UAAAX,EAAA,CAAAU,SAAA,aAAwC;UAACV,EAAA,CAAAW,MAAA,YACzC;UAAAX,EAAA,CAAAS,cAAA,cAAQ;UAAAT,EAAA,CAAAW,MAAA,sBAAc;UAGlCX,EAHkC,CAAAY,YAAA,EAAS,EAC/B,EACF,EACJ;UAKMZ,EAHZ,CAAAS,cAAA,eAAsB,eACc,gBACF,aAIjB;UAAAT,EAAA,CAAAW,MAAA,sBAAc;UAAAX,EAAA,CAAAY,YAAA,EAClB;UACDZ,EAAA,CAAAS,cAAA,aAGK;UAAAT,EAAA,CAAAW,MAAA,wBAAgB;UAAAX,EAAA,CAAAY,YAAA,EACpB;UACDZ,EAAA,CAAAS,cAAA,aACK;UAAAT,EAAA,CAAAW,MAAA,qBAAa;UAMtCX,EANsC,CAAAY,YAAA,EACjB,EACG,EACN,EACJ,EACJ,EACD;;;UAjCWZ,EAAA,CAAAa,SAAA,IAEJ;UAFIb,EAAA,CAAAc,kBAAA,aAAAN,GAAA,CAAAd,WAAA,2DAEJ;;;qBA3JVJ,YAAY,EAAEC,YAAY,EAAAwB,EAAA,CAAAC,UAAA;MAAAC,MAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}