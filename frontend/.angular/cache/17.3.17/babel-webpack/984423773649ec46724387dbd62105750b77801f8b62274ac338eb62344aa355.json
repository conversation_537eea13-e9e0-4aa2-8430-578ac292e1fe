{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n/**\n * Footer component for the application.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nlet FooterComponent = class FooterComponent {\n  constructor() {\n    this.currentYear = new Date().getFullYear();\n  }\n};\nFooterComponent = __decorate([Component({\n  selector: 'app-footer',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <footer class=\"footer-custom mt-auto\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <div class=\"col-md-6 col-lg-4 mb-4\">\n            <h5 class=\"text-white mb-3\">\n              <i class=\"fas fa-shopping-cart me-2\"></i>FreshMart\n            </h5>\n            <p class=\"text-light\">\n              Your trusted partner for fresh groceries and quality products. \n              We deliver farm-fresh produce right to your doorstep.\n            </p>\n            <div class=\"social-links\">\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Facebook\">\n                <i class=\"fab fa-facebook-f\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Twitter\">\n                <i class=\"fab fa-twitter\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Instagram\">\n                <i class=\"fab fa-instagram\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light\" aria-label=\"LinkedIn\">\n                <i class=\"fab fa-linkedin-in\"></i>\n              </a>\n            </div>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-2 mb-4\">\n            <h6 class=\"text-white mb-3\">Quick Links</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2\">\n                <a routerLink=\"/\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-home me-2\"></i>Home\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/products\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-shopping-basket me-2\"></i>Products\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/products/search\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-search me-2\"></i>Search\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/register\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-user-plus me-2\"></i>Register\n                </a>\n              </li>\n            </ul>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-3 mb-4\">\n            <h6 class=\"text-white mb-3\">Categories</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-apple-alt me-2\"></i>Fruits\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-carrot me-2\"></i>Vegetables\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-cheese me-2\"></i>Dairy\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-bread-slice me-2\"></i>Bakery\n                </a>\n              </li>\n            </ul>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-3 mb-4\">\n            <h6 class=\"text-white mb-3\">Contact Info</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-map-marker-alt me-2\"></i>\n                123 Grocery Street, Food City, FC 12345\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-phone me-2\"></i>\n                +****************\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-envelope me-2\"></i>\n                <EMAIL>\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-clock me-2\"></i>\n                24/7 Customer Support\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <hr class=\"my-4 bg-light\">\n        \n        <div class=\"row align-items-center\">\n          <div class=\"col-md-6\">\n            <p class=\"text-light mb-0\">\n              &copy; {{ currentYear }} Online Grocery Ordering System. All rights reserved.\n            </p>\n          </div>\n          <div class=\"col-md-6 text-md-end\">\n            <p class=\"text-light mb-0\">\n              Developed with <i class=\"fas fa-heart text-danger\"></i> by \n              <strong>Chirag Singhal</strong>\n            </p>\n          </div>\n        </div>\n        \n        <div class=\"row mt-3\">\n          <div class=\"col-12 text-center\">\n            <small class=\"text-light\">\n              <a href=\"#\" class=\"text-light text-decoration-none me-3\">Privacy Policy</a>\n              <a href=\"#\" class=\"text-light text-decoration-none me-3\">Terms of Service</a>\n              <a href=\"#\" class=\"text-light text-decoration-none\">Cookie Policy</a>\n            </small>\n          </div>\n        </div>\n      </div>\n    </footer>\n  `,\n  styles: [`\n    .footer-custom {\n      background: #343a40;\n      color: white;\n      padding: 3rem 0 1rem;\n    }\n    \n    .social-links a {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      line-height: 40px;\n      text-align: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n    }\n    \n    .social-links a:hover {\n      background: rgba(255, 255, 255, 0.2);\n      transform: translateY(-2px);\n    }\n    \n    .list-unstyled a {\n      transition: color 0.3s ease;\n    }\n    \n    .list-unstyled a:hover {\n      color: #667eea !important;\n    }\n    \n    hr {\n      opacity: 0.3;\n    }\n    \n    @media (max-width: 768px) {\n      .footer-custom {\n        padding: 2rem 0 1rem;\n      }\n      \n      .col-md-6.text-md-end {\n        text-align: center !important;\n        margin-top: 1rem;\n      }\n    }\n  `]\n})], FooterComponent);\nexport { FooterComponent };", "map": {"version": 3, "names": ["Component", "CommonModule", "RouterModule", "FooterComponent", "constructor", "currentYear", "Date", "getFullYear", "__decorate", "selector", "standalone", "imports", "template", "styles"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/components/shared/footer/footer.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n/**\n * Footer component for the application.\n * \n * <AUTHOR>\n * @version 1.0.0\n */\n@Component({\n  selector: 'app-footer',\n  standalone: true,\n  imports: [CommonModule, RouterModule],\n  template: `\n    <footer class=\"footer-custom mt-auto\">\n      <div class=\"container\">\n        <div class=\"row\">\n          <div class=\"col-md-6 col-lg-4 mb-4\">\n            <h5 class=\"text-white mb-3\">\n              <i class=\"fas fa-shopping-cart me-2\"></i>FreshMart\n            </h5>\n            <p class=\"text-light\">\n              Your trusted partner for fresh groceries and quality products. \n              We deliver farm-fresh produce right to your doorstep.\n            </p>\n            <div class=\"social-links\">\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Facebook\">\n                <i class=\"fab fa-facebook-f\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Twitter\">\n                <i class=\"fab fa-twitter\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light me-3\" aria-label=\"Instagram\">\n                <i class=\"fab fa-instagram\"></i>\n              </a>\n              <a href=\"#\" class=\"text-light\" aria-label=\"LinkedIn\">\n                <i class=\"fab fa-linkedin-in\"></i>\n              </a>\n            </div>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-2 mb-4\">\n            <h6 class=\"text-white mb-3\">Quick Links</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2\">\n                <a routerLink=\"/\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-home me-2\"></i>Home\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/products\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-shopping-basket me-2\"></i>Products\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/products/search\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-search me-2\"></i>Search\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a routerLink=\"/register\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-user-plus me-2\"></i>Register\n                </a>\n              </li>\n            </ul>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-3 mb-4\">\n            <h6 class=\"text-white mb-3\">Categories</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-apple-alt me-2\"></i>Fruits\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-carrot me-2\"></i>Vegetables\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-cheese me-2\"></i>Dairy\n                </a>\n              </li>\n              <li class=\"mb-2\">\n                <a href=\"#\" class=\"text-light text-decoration-none\">\n                  <i class=\"fas fa-bread-slice me-2\"></i>Bakery\n                </a>\n              </li>\n            </ul>\n          </div>\n          \n          <div class=\"col-md-6 col-lg-3 mb-4\">\n            <h6 class=\"text-white mb-3\">Contact Info</h6>\n            <ul class=\"list-unstyled\">\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-map-marker-alt me-2\"></i>\n                123 Grocery Street, Food City, FC 12345\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-phone me-2\"></i>\n                +****************\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-envelope me-2\"></i>\n                <EMAIL>\n              </li>\n              <li class=\"mb-2 text-light\">\n                <i class=\"fas fa-clock me-2\"></i>\n                24/7 Customer Support\n              </li>\n            </ul>\n          </div>\n        </div>\n        \n        <hr class=\"my-4 bg-light\">\n        \n        <div class=\"row align-items-center\">\n          <div class=\"col-md-6\">\n            <p class=\"text-light mb-0\">\n              &copy; {{ currentYear }} Online Grocery Ordering System. All rights reserved.\n            </p>\n          </div>\n          <div class=\"col-md-6 text-md-end\">\n            <p class=\"text-light mb-0\">\n              Developed with <i class=\"fas fa-heart text-danger\"></i> by \n              <strong>Chirag Singhal</strong>\n            </p>\n          </div>\n        </div>\n        \n        <div class=\"row mt-3\">\n          <div class=\"col-12 text-center\">\n            <small class=\"text-light\">\n              <a href=\"#\" class=\"text-light text-decoration-none me-3\">Privacy Policy</a>\n              <a href=\"#\" class=\"text-light text-decoration-none me-3\">Terms of Service</a>\n              <a href=\"#\" class=\"text-light text-decoration-none\">Cookie Policy</a>\n            </small>\n          </div>\n        </div>\n      </div>\n    </footer>\n  `,\n  styles: [`\n    .footer-custom {\n      background: #343a40;\n      color: white;\n      padding: 3rem 0 1rem;\n    }\n    \n    .social-links a {\n      display: inline-block;\n      width: 40px;\n      height: 40px;\n      line-height: 40px;\n      text-align: center;\n      border-radius: 50%;\n      background: rgba(255, 255, 255, 0.1);\n      transition: all 0.3s ease;\n    }\n    \n    .social-links a:hover {\n      background: rgba(255, 255, 255, 0.2);\n      transform: translateY(-2px);\n    }\n    \n    .list-unstyled a {\n      transition: color 0.3s ease;\n    }\n    \n    .list-unstyled a:hover {\n      color: #667eea !important;\n    }\n    \n    hr {\n      opacity: 0.3;\n    }\n    \n    @media (max-width: 768px) {\n      .footer-custom {\n        padding: 2rem 0 1rem;\n      }\n      \n      .col-md-6.text-md-end {\n        text-align: center !important;\n        margin-top: 1rem;\n      }\n    }\n  `]\n})\nexport class FooterComponent {\n  currentYear = new Date().getFullYear();\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAQ,eAAe;AACzC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;;;;;;AA4LO,IAAMC,eAAe,GAArB,MAAMA,eAAe;EAArBC,YAAA;IACL,KAAAC,WAAW,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;EACxC;CAAC;AAFYJ,eAAe,GAAAK,UAAA,EAtL3BR,SAAS,CAAC;EACTS,QAAQ,EAAE,YAAY;EACtBC,UAAU,EAAE,IAAI;EAChBC,OAAO,EAAE,CAACV,YAAY,EAAEC,YAAY,CAAC;EACrCU,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAkIT;EACDC,MAAM,EAAE,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA6CR;CACF,CAAC,C,EACWV,eAAe,CAE3B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}