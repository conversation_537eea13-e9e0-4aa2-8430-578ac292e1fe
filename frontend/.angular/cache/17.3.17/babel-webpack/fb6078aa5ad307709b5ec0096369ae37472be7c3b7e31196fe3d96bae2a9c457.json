{"ast": null, "code": "export const routes = [{\n  path: \"\",\n  loadComponent: () => import(\"./components/home/<USER>\").then(m => m.HomeComponent)\n}, {\n  path: \"**\",\n  redirectTo: \"\"\n}];", "map": {"version": 3, "names": ["routes", "path", "loadComponent", "then", "m", "HomeComponent", "redirectTo"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/app.routes.ts"], "sourcesContent": ["import { Routes } from \"@angular/router\";\n\nexport const routes: Routes = [\n    {\n        path: \"\",\n        loadComponent: () =>\n            import(\"./components/home/<USER>\").then(\n                (m) => m.HomeComponent\n            ),\n    },\n    {\n        path: \"**\",\n        redirectTo: \"\",\n    },\n];\n"], "mappings": "AAEA,OAAO,MAAMA,MAAM,GAAW,CAC1B;EACIC,IAAI,EAAE,EAAE;EACRC,aAAa,EAAEA,CAAA,KACX,MAAM,CAAC,kCAAkC,CAAC,CAACC,IAAI,CAC1CC,CAAC,IAAKA,CAAC,CAACC,aAAa;CAEjC,EACD;EACIJ,IAAI,EAAE,IAAI;EACVK,UAAU,EAAE;CACf,CACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}