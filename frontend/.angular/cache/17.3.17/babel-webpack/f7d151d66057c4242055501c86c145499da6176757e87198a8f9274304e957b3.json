{"ast": null, "code": "/**\n * Authentication model interfaces for the grocery ordering system.\n *\n * <AUTHOR>\n * @version 1.0.0\n */\nexport var UserRole;\n(function (UserRole) {\n  UserRole[\"CUSTOMER\"] = \"CUSTOMER\";\n  UserRole[\"ADMIN\"] = \"ADMIN\";\n  UserRole[\"SUPER_ADMIN\"] = \"SUPER_ADMIN\";\n})(UserRole || (UserRole = {}));", "map": {"version": 3, "names": ["UserRole"], "sources": ["/workspaces/Online-Grocery-Ordering-System-6/frontend/src/app/models/auth.model.ts"], "sourcesContent": ["/**\n * Authentication model interfaces for the grocery ordering system.\n * \n * <AUTHOR>\n * @version 1.0.0\n */\n\nexport interface LoginRequest {\n  username: string;\n  password: string;\n}\n\nexport interface AuthResponse {\n  token?: string;\n  type?: string;\n  id?: number;\n  username?: string;\n  email?: string;\n  role?: string;\n  message?: string;\n  success: boolean;\n}\n\nexport interface User {\n  id: number;\n  username: string;\n  email: string;\n  role: UserRole;\n  isActive: boolean;\n}\n\nexport enum UserRole {\n  CUSTOMER = 'CUSTOMER',\n  ADMIN = 'ADMIN',\n  SUPER_ADMIN = 'SUPER_ADMIN'\n}\n\nexport interface TokenPayload {\n  sub: string; // username\n  iat: number; // issued at\n  exp: number; // expiration\n  role: string;\n  id: number;\n}\n\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  message: string;\n  data?: T;\n  timestamp: number;\n}\n\nexport interface ErrorResponse {\n  success: false;\n  message: string;\n  error?: string;\n  status?: number;\n  timestamp: number;\n}\n"], "mappings": "AAAA;;;;;;AA+BA,WAAYA,QAIX;AAJD,WAAYA,QAAQ;EAClBA,QAAA,yBAAqB;EACrBA,QAAA,mBAAe;EACfA,QAAA,+BAA2B;AAC7B,CAAC,EAJWA,QAAQ,KAARA,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}