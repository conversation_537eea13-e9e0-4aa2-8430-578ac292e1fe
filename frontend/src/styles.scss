/* Global Styles for Online Grocery Ordering System */

/* Import Angular Material theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* Custom CSS Variables */
:root {
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --success-color: #28a745;
  --danger-color: #dc3545;
  --warning-color: #ffc107;
  --info-color: #17a2b8;
  --light-color: #f8f9fa;
  --dark-color: #343a40;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Global Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Roboto', sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

/* Custom Button Styles */
.btn-primary-custom {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  padding: 10px 20px;
  font-weight: 500;
  transition: var(--transition);
}

.btn-primary-custom:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  color: white;
}

.btn-success-custom {
  background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
  border: none;
  border-radius: var(--border-radius);
  color: white;
  padding: 10px 20px;
  font-weight: 500;
  transition: var(--transition);
}

.btn-success-custom:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3);
  color: white;
}

/* Card Styles */
.card-custom {
  border: none;
  border-radius: 15px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
}

.card-custom:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Form Styles */
.form-control-custom {
  border: 2px solid #e9ecef;
  border-radius: var(--border-radius);
  padding: 12px 15px;
  transition: var(--transition);
}

.form-control-custom:focus {
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

/* Alert Styles */
.alert-custom {
  border: none;
  border-radius: var(--border-radius);
  padding: 15px 20px;
}

/* Loading Spinner */
.spinner-custom {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Navbar Styles */
.navbar-custom {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  box-shadow: var(--box-shadow);
}

.navbar-brand-custom {
  font-weight: bold;
  font-size: 1.5rem;
  color: white !important;
}

/* Footer Styles */
.footer-custom {
  background: var(--dark-color);
  color: white;
  padding: 2rem 0;
  margin-top: auto;
}

/* Product Card Styles */
.product-card {
  border: none;
  border-radius: 10px;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  overflow: hidden;
}

.product-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.product-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.product-price {
  font-size: 1.25rem;
  font-weight: bold;
  color: var(--success-color);
}

/* Badge Styles */
.badge-custom {
  padding: 8px 12px;
  border-radius: 20px;
  font-size: 0.875rem;
}

/* Table Styles */
.table-custom {
  background: white;
  border-radius: var(--border-radius);
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.table-custom th {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: white;
  border: none;
  padding: 15px;
}

.table-custom td {
  padding: 15px;
  border-color: #e9ecef;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container-fluid {
    padding: 0 15px;
  }
  
  .card-custom {
    margin-bottom: 20px;
  }
  
  .btn-primary-custom,
  .btn-success-custom {
    width: 100%;
    margin-bottom: 10px;
  }
}

/* Utility Classes */
.text-primary-custom {
  color: var(--primary-color) !important;
}

.text-success-custom {
  color: var(--success-color) !important;
}

.bg-primary-custom {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
}

.bg-light-custom {
  background-color: var(--light-color) !important;
}

/* Animation Classes */
.fade-in {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

.slide-in {
  animation: slideIn 0.5s ease-out;
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* Material Design Overrides */
.mat-mdc-card {
  border-radius: 15px !important;
  box-shadow: var(--box-shadow) !important;
}

.mat-mdc-button {
  border-radius: var(--border-radius) !important;
}

.mat-mdc-form-field {
  width: 100%;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-color);
}
